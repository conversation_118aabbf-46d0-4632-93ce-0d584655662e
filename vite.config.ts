import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { crx } from '@crxjs/vite-plugin'
import type { ManifestV3Export } from '@crxjs/vite-plugin'
import manifest from './public/manifest.json'
import wasm from 'vite-plugin-wasm'
import topLevelAwait from 'vite-plugin-top-level-await'
import path from 'path'

// Convert manifest to the correct type
const manifestV3 = manifest as ManifestV3Export

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // Explicitly configure JSX runtime for React 19
      jsxRuntime: 'automatic',
      jsxImportSource: 'react'
    }),
    crx({ manifest: manifestV3 }),
    wasm(),
    topLevelAwait()
  ],
  build: {
    rollupOptions: {
      input: {
        popup: 'index.html',
        options: 'options.html'
      },
      output: {
        // Ensure proper asset naming for Chrome Extension
        assetFileNames: (_assetInfo) => {
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    target: 'esnext',
    // Basic build settings for Chrome Extension
    copyPublicDir: true,
    chunkSizeWarningLimit: 1000,
    // Disable minification for easier debugging
    minify: false,
    sourcemap: true
  },
  worker: {
    format: 'es'
  },
  optimizeDeps: {
    // Configure esbuild for modern JS
    esbuildOptions: {
      target: 'esnext',
      format: 'esm'
    }
  },
  server: {
    // Basic development server configuration
    fs: {
      allow: ['..']
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  // Define environment variables
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    'process.env.EXTENSION_MODE': JSON.stringify(true)
  }
})