/**
 * LunrIndexStorage - Manages persistence of Lunr search index in IndexedDB
 * 
 * This module handles serialization, storage, and retrieval of Lunr.js indexes
 * to avoid rebuilding the index on every page load. It includes versioning
 * support to handle index schema changes.
 * 
 * @module LunrIndexStorage
 * @version 1.0
 * @since 2025-06-27
 */

import { logger } from '../../utils/logger';
import { dbService } from '../../services/db.service';
import type { Page } from '../../models/db.model';

/**
 * Interface for stored Lunr index data
 */
export interface StoredLunrIndex {
  indexData: string;          // Serialized Lunr index
  documentIds: string[];      // IDs of documents in the index
  version: number;            // Index version for compatibility
  createdAt: number;          // Timestamp of index creation
  documentCount: number;      // Number of documents indexed
  checksum?: string;          // Optional checksum for validation
}

/**
 * Storage key for Lunr index in IndexedDB
 */
const LUNR_INDEX_KEY = 'LUNR_FULLTEXT_INDEX';
const CURRENT_INDEX_VERSION = 1;

/**
 * LunrIndexStorage class for managing index persistence
 */
export class LunrIndexStorage {
  private readonly maxIndexAge = 7 * 24 * 60 * 60 * 1000; // 7 days

  /**
   * Save Lunr index to IndexedDB
   * @param indexData Serialized Lunr index data
   * @param documentIds Array of document IDs in the index
   * @returns Promise resolving when save is complete
   */
  async saveIndex(indexData: string, documentIds: string[]): Promise<void> {
    try {
      const storedIndex: StoredLunrIndex = {
        indexData,
        documentIds,
        version: CURRENT_INDEX_VERSION,
        createdAt: Date.now(),
        documentCount: documentIds.length,
        checksum: this.generateChecksum(indexData)
      };

      // Store in IndexedDB using db service
      await dbService.setSetting(LUNR_INDEX_KEY, storedIndex);
      
      logger.info(`Lunr index saved: ${documentIds.length} documents, version ${CURRENT_INDEX_VERSION}`);
    } catch (error) {
      logger.error('Failed to save Lunr index:', error);
      throw new Error(`Failed to save Lunr index: ${error}`);
    }
  }

  /**
   * Load Lunr index from IndexedDB
   * @returns Promise resolving to stored index data or null if not found/invalid
   */
  async loadIndex(): Promise<StoredLunrIndex | null> {
    try {
      const storedData = await dbService.getSetting(LUNR_INDEX_KEY) as StoredLunrIndex | null;
      
      if (!storedData) {
        logger.debug('No stored Lunr index found');
        return null;
      }

      // Validate stored index
      if (!this.isValidStoredIndex(storedData)) {
        logger.warn('Stored Lunr index is invalid or outdated');
        await this.clearIndex();
        return null;
      }

      logger.info(`Lunr index loaded: ${storedData.documentCount} documents, version ${storedData.version}`);
      return storedData;
    } catch (error) {
      logger.error('Failed to load Lunr index:', error);
      return null;
    }
  }

  /**
   * Check if index needs rebuild based on document changes
   * @param storedIndex The stored index data
   * @param currentPages Current pages in the database
   * @returns True if index needs rebuild
   */
  async needsRebuild(storedIndex: StoredLunrIndex, currentPages: Page[]): Promise<boolean> {
    try {
      // Check if index is too old
      if (this.isIndexExpired(storedIndex)) {
        logger.info('Lunr index expired, rebuild needed');
        return true;
      }

      // Check version mismatch
      if (storedIndex.version !== CURRENT_INDEX_VERSION) {
        logger.info(`Lunr index version mismatch: ${storedIndex.version} vs ${CURRENT_INDEX_VERSION}`);
        return true;
      }

      // Quick count check
      if (storedIndex.documentCount !== currentPages.length) {
        logger.info(`Document count mismatch: ${storedIndex.documentCount} vs ${currentPages.length}`);
        return true;
      }

      // Check if all current page IDs are in the index
      const storedIdSet = new Set(storedIndex.documentIds);
      const hasNewPages = currentPages.some(page => !storedIdSet.has(page.id));
      
      if (hasNewPages) {
        logger.info('New pages found that are not in index');
        return true;
      }

      // Check for modified pages by comparing timestamps
      const pageMap = new Map(currentPages.map(p => [p.id, p]));
      for (const id of storedIndex.documentIds) {
        const page = pageMap.get(id);
        if (!page) {
          // Page was deleted
          logger.info(`Page ${id} was deleted, rebuild needed`);
          return true;
        }
        
        // Check if page was modified after index creation
        if (page.lastUpdated && page.lastUpdated > storedIndex.createdAt) {
          logger.info(`Page ${id} was modified after index creation`);
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.error('Error checking if index needs rebuild:', error);
      return true; // Rebuild on error
    }
  }

  /**
   * Clear stored Lunr index
   */
  async clearIndex(): Promise<void> {
    try {
      await dbService.deleteSetting(LUNR_INDEX_KEY);
      logger.info('Lunr index cleared from storage');
    } catch (error) {
      logger.error('Failed to clear Lunr index:', error);
    }
  }

  /**
   * Get index statistics
   */
  async getIndexStats(): Promise<{
    exists: boolean;
    documentCount: number;
    version: number;
    age: number;
    sizeKB: number;
  } | null> {
    try {
      const storedIndex = await this.loadIndex();
      if (!storedIndex) {
        return null;
      }

      const ageMs = Date.now() - storedIndex.createdAt;
      const sizeKB = Math.round(storedIndex.indexData.length / 1024);

      return {
        exists: true,
        documentCount: storedIndex.documentCount,
        version: storedIndex.version,
        age: Math.round(ageMs / 1000 / 60), // Age in minutes
        sizeKB
      };
    } catch (error) {
      logger.error('Failed to get index stats:', error);
      return null;
    }
  }

  /**
   * Validate stored index structure
   * @private
   */
  private isValidStoredIndex(data: any): data is StoredLunrIndex {
    return (
      data &&
      typeof data.indexData === 'string' &&
      Array.isArray(data.documentIds) &&
      typeof data.version === 'number' &&
      typeof data.createdAt === 'number' &&
      typeof data.documentCount === 'number'
    );
  }

  /**
   * Check if index is expired
   * @private
   */
  private isIndexExpired(storedIndex: StoredLunrIndex): boolean {
    const age = Date.now() - storedIndex.createdAt;
    return age > this.maxIndexAge;
  }

  /**
   * Generate a simple checksum for validation
   * @private
   */
  private generateChecksum(data: string): string {
    // Simple checksum using string length and first/last characters
    const len = data.length;
    const first = data.charCodeAt(0) || 0;
    const last = data.charCodeAt(len - 1) || 0;
    const mid = data.charCodeAt(Math.floor(len / 2)) || 0;
    return `${len}-${first}-${mid}-${last}`;
  }
}

// Export singleton instance
export const lunrIndexStorage = new LunrIndexStorage();