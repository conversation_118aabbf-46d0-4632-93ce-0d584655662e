/**
 * StringMatcher - High-Performance String Matching Algorithm
 * 
 * Implements efficient string matching for hybrid search using Levenshtein distance
 * and optimized similarity scoring. Supports exact and fuzzy matching across
 * page titles, URLs, and content.
 * 
 * @module StringMatcher
 * @version 4.0
 * @since 2025-06-23
 */

import type { Page } from '../../models';
import type { SearchResult } from './HybridSearchEngine';

/**
 * Configuration options for StringMatcher
 */
export interface StringMatcherConfig {
  /** Minimum similarity threshold (0.0 to 1.0) */
  minSimilarity?: number;
  /** Maximum number of results to return */
  maxResults?: number;
  /** Weight for title matches (higher = more important) */
  titleWeight?: number;
  /** Weight for URL matches */
  urlWeight?: number;
  /** Weight for content matches */
  contentWeight?: number;
  /** Enable case-sensitive matching */
  caseSensitive?: boolean;
}

/**
 * Default configuration values
 */
const DEFAULT_CONFIG: Required<StringMatcherConfig> = {
  minSimilarity: 0.1,
  maxResults: 100,
  titleWeight: 1.0,
  urlWeight: 0.8,
  contentWeight: 0.6,
  caseSensitive: false
};

/**
 * StringMatcher class for efficient string-based search
 */
export class StringMatcher {
  private config: Required<StringMatcherConfig>;

  /**
   * Initialize StringMatcher with optional configuration
   */
  constructor(config: StringMatcherConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Search pages using string matching algorithms
   * 
   * @param query - Search query string
   * @param pages - Array of pages to search
   * @returns Array of search results sorted by string score
   */
  search(query: string, pages: Page[]): SearchResult[] {
    // Handle edge cases
    if (!query || typeof query !== 'string' || !query.trim()) {
      return [];
    }

    if (!pages || pages.length === 0) {
      return [];
    }

    const normalizedQuery = this.normalizeString(query);
    const queryTerms = this.extractTerms(normalizedQuery);

    const results: SearchResult[] = [];
    let exactTitleMatch: SearchResult | null = null;

    // Process each page for string matching
    for (const page of pages) {
      const score = this.calculateStringScore(queryTerms, page);
      
      if (score >= this.config.minSimilarity) {
        const result: SearchResult = {
          id: page.id,
          url: page.url,
          title: page.title,
          content: page.content,
          lastVisitTime: page.visitTime,
          visitCount: page.accessCount,
          domain: page.domain,
          language: page.language,
          
          stringScore: score,
          fulltextScore: 0, // Not applicable for string search
          combinedScore: score, // Will be recalculated in hybrid search
          relevanceScore: score,
          
          // Additional metadata
          snippet: this.generateSnippet(page.content, queryTerms),
          highlights: this.generateHighlights(page, queryTerms)
        };

        // Check for exact title match
        if (score === 1.0 && this.normalizeString(page.title) === normalizedQuery) {
          exactTitleMatch = result;
        } else {
          results.push(result);
        }
      }
    }

    // If we have an exact title match, return only that
    if (exactTitleMatch) {
      return [exactTitleMatch];
    }

    // Sort by string score descending
    results.sort((a, b) => b.stringScore - a.stringScore);

    // Apply max results limit
    return results.slice(0, this.config.maxResults);
  }

  /**
   * Normalize string for comparison (case, spacing, special chars)
   */
  private normalizeString(text: string): string {
    if (!this.config.caseSensitive) {
      text = text.toLowerCase();
    }
    
    // Normalize whitespace and remove extra spaces
    return text.trim().replace(/\s+/g, ' ');
  }

  /**
   * Extract search terms from normalized query
   */
  private extractTerms(normalizedQuery: string): string[] {
    // Split on whitespace and filter empty terms
    return normalizedQuery.split(/\s+/).filter(term => term.length > 0);
  }

  /**
   * Calculate string similarity score for a page
   */
  private calculateStringScore(queryTerms: string[], page: Page): number {
    const normalizedTitle = this.normalizeString(page.title);
    const normalizedUrl = this.normalizeString(page.url);
    const normalizedContent = this.normalizeString(page.content);
    const normalizedQuery = queryTerms.join(' ');

    // Check for exact title match first
    if (normalizedTitle === normalizedQuery) {
      return 1.0;
    }

    let maxScore = 0;

    // Title matching (highest weight)
    const titleScore = this.calculateTextSimilarity(normalizedQuery, normalizedTitle);
    maxScore = Math.max(maxScore, titleScore * this.config.titleWeight);

    // URL matching
    const urlScore = this.calculateTextSimilarity(normalizedQuery, normalizedUrl);
    maxScore = Math.max(maxScore, urlScore * this.config.urlWeight);

    // Content matching (lowest weight due to length)
    const contentScore = this.calculateContentSimilarity(queryTerms, normalizedContent);
    maxScore = Math.max(maxScore, contentScore * this.config.contentWeight);

    // Term-based scoring for partial matches
    const termScore = this.calculateTermScore(queryTerms, {
      title: normalizedTitle,
      url: normalizedUrl,
      content: normalizedContent
    });
    maxScore = Math.max(maxScore, termScore);

    // TASK-2.1: Add phrase matching bonus for consecutive term matches
    const phraseBonus = this.calculatePhraseBonus(queryTerms, {
      title: normalizedTitle,
      url: normalizedUrl,
      content: normalizedContent
    });
    maxScore += phraseBonus;

    // TASK-2.3: Calculate word order and proximity scoring
    const orderProximityScore = this.calculateOrderAndProximityScore(queryTerms, {
      title: normalizedTitle,
      url: normalizedUrl,
      content: normalizedContent
    });
    maxScore += orderProximityScore;

    // TASK-2.4: Apply exact match boosting
    // Pass both original and normalized fields for proper case detection
    const exactMatchBoost = this.calculateExactMatchBoost(
      queryTerms.join(' '), // Original query (preserves case)
      {
        title: page.title,
        url: page.url,
        content: page.content
      },
      {
        title: normalizedTitle,
        url: normalizedUrl,
        content: normalizedContent
      }
    );
    maxScore *= exactMatchBoost;

    return Math.min(maxScore, 1.0);
  }

  /**
   * Calculate similarity between two text strings using optimized algorithm
   */
  private calculateTextSimilarity(query: string, text: string): number {
    if (query === text) return 1.0;
    if (!query || !text) return 0;

    // For performance, use simple substring matching for long texts
    if (text.length > 500 || query.length > 100) {
      return text.includes(query) ? 0.8 : 0;
    }

    // Use Levenshtein-based similarity for shorter texts
    return this.levenshteinSimilarity(query, text);
  }

  /**
   * Calculate content similarity using term frequency with prefix matching support
   */
  private calculateContentSimilarity(queryTerms: string[], content: string): number {
    if (!content || queryTerms.length === 0) return 0;

    const normalizedContent = content.toLowerCase();
    let totalMatchScore = 0;

    // Calculate match score for each term (including prefix matches)
    for (const term of queryTerms) {
      const matchScore = this.calculateFieldTermScore(term, content);
      totalMatchScore += matchScore;
    }

    const termRatio = totalMatchScore / queryTerms.length;
    
    // For proximity bonus, only use exact matches to avoid complexity
    const exactMatchedTerms = queryTerms.filter(term => 
      normalizedContent.includes(term.toLowerCase())
    );
    const proximityBonus = this.calculateProximityBonus(exactMatchedTerms, content);
    
    return Math.min(termRatio * 0.7 + proximityBonus * 0.3, 1.0);
  }

  /**
   * Calculate term-based score across title, URL, and content
   * Enhanced with prefix matching support
   */
  private calculateTermScore(queryTerms: string[], fields: {
    title: string;
    url: string;
    content: string;
  }): number {
    let totalScore = 0;
    let maxPossibleScore = 0;

    for (const term of queryTerms) {
      let termScore = 0;

      // Title matches get highest score (exact + prefix)
      const titleMatch = this.calculateFieldTermScore(term, fields.title);
      if (titleMatch > 0) {
        termScore = Math.max(termScore, this.config.titleWeight * titleMatch);
      }

      // URL matches get medium score (exact + prefix)
      const urlMatch = this.calculateFieldTermScore(term, fields.url);
      if (urlMatch > 0) {
        termScore = Math.max(termScore, this.config.urlWeight * urlMatch);
      }

      // Content matches get lower score (exact + prefix)
      const contentMatch = this.calculateFieldTermScore(term, fields.content);
      if (contentMatch > 0) {
        termScore = Math.max(termScore, this.config.contentWeight * contentMatch);
      }

      totalScore += termScore;
      maxPossibleScore += this.config.titleWeight; // Max possible per term
    }

    return maxPossibleScore > 0 ? totalScore / maxPossibleScore : 0;
  }

  /**
   * Calculate score for a term in a specific field with prefix matching support
   * Returns a score between 0 and 1, where 1 is exact match and lower values are prefix matches
   * TASK-2.2: Improved prefix matching algorithm
   */
  private calculateFieldTermScore(term: string, field: string): number {
    if (!term || !field) return 0;

    const normalizedTerm = term.toLowerCase();
    const normalizedField = field.toLowerCase();

    // Exact substring match gets full score
    if (normalizedField.includes(normalizedTerm)) {
      return 1.0;
    }

    // Split field into words for prefix matching
    const words = normalizedField.split(/\s+/);
    let bestScore = 0;

    for (const word of words) {
      if (word.startsWith(normalizedTerm)) {
        // TASK-2.2: Adjust prefix score formula
        const prefixLength = normalizedTerm.length;
        const wordLength = word.length;
        const prefixRatio = prefixLength / wordLength;
        
        // TASK-2.2: Lower minimum prefix threshold to 0.2 (20%)
        if (prefixRatio >= 0.2) {
          // TASK-2.2: New scoring formula: prefixLength/wordLength * 0.8 + 0.2
          const score = prefixRatio * 0.8 + 0.2;
          
          // TASK-2.2: Add early termination bonus for shorter queries
          // Shorter queries get additional bonus to improve their relevance
          const queryLengthBonus = normalizedTerm.length <= 3 ? 0.1 : 0;
          
          bestScore = Math.max(bestScore, Math.min(score + queryLengthBonus, 1.0));
        }
      }
    }

    return bestScore;
  }

  /**
   * Calculate proximity bonus for terms that appear close together
   */
  private calculateProximityBonus(matchedTerms: string[], content: string): number {
    if (matchedTerms.length < 2) return 0;

    // Find positions of matched terms
    const positions: number[] = [];
    for (const term of matchedTerms) {
      const pos = content.indexOf(term);
      if (pos >= 0) {
        positions.push(pos);
      }
    }

    if (positions.length < 2) return 0;

    // Calculate average distance between terms
    positions.sort((a, b) => a - b);
    let totalDistance = 0;
    for (let i = 1; i < positions.length; i++) {
      totalDistance += positions[i] - positions[i - 1];
    }

    const avgDistance = totalDistance / (positions.length - 1);
    
    // Closer terms get higher bonus (inversely proportional to distance)
    return Math.max(0, Math.min(0.3, 100 / avgDistance));
  }

  /**
   * TASK-2.1: Calculate phrase matching bonus for consecutive term matches
   * Implements sliding window phrase detection with bonus calculation
   */
  private calculatePhraseBonus(queryTerms: string[], fields: {
    title: string;
    url: string;
    content: string;
  }): number {
    if (queryTerms.length < 2) return 0;

    let maxBonus = 0;

    // Check each field for phrase matches
    const fieldWeights = {
      title: this.config.titleWeight,
      url: this.config.urlWeight,
      content: this.config.contentWeight
    };

    for (const [fieldName, fieldText] of Object.entries(fields)) {
      const fieldWeight = fieldWeights[fieldName as keyof typeof fieldWeights];
      const phraseBonus = this.findPhraseMatches(queryTerms, fieldText);
      maxBonus = Math.max(maxBonus, phraseBonus * fieldWeight);
    }

    return maxBonus;
  }

  /**
   * Find phrase matches using sliding window approach
   */
  private findPhraseMatches(queryTerms: string[], text: string): number {
    const normalizedText = text.toLowerCase();
    let maxPhraseLength = 0;
    
    // Try to find the longest consecutive sequence of query terms
    for (let startIdx = 0; startIdx < queryTerms.length; startIdx++) {
      let currentPhrase = queryTerms[startIdx].toLowerCase();
      let phraseLength = 1;
      
      // Check if we can extend the phrase
      for (let i = startIdx + 1; i < queryTerms.length; i++) {
        const extendedPhrase = currentPhrase + ' ' + queryTerms[i].toLowerCase();
        
        if (normalizedText.includes(extendedPhrase)) {
          currentPhrase = extendedPhrase;
          phraseLength++;
        } else {
          break;
        }
      }
      
      maxPhraseLength = Math.max(maxPhraseLength, phraseLength);
    }
    
    // TASK-2.1: Bonus calculation: matchedPhraseLength * 0.2
    // Only apply bonus for phrases of length 2 or more
    if (maxPhraseLength >= 2) {
      return maxPhraseLength * 0.2;
    }
    
    return 0;
  }

  /**
   * TASK-2.3: Calculate word order and proximity scoring
   * Combines term order preservation score and proximity bonus
   * @returns Combined score: orderScore * 0.3 + proximityScore * 0.2
   */
  private calculateOrderAndProximityScore(queryTerms: string[], fields: {
    title: string;
    url: string;
    content: string;
  }): number {
    if (queryTerms.length < 2) return 0;

    let maxScore = 0;
    const fieldWeights = {
      title: this.config.titleWeight,
      url: this.config.urlWeight,
      content: this.config.contentWeight
    };

    // Calculate scores for each field
    for (const [fieldName, fieldText] of Object.entries(fields)) {
      const fieldWeight = fieldWeights[fieldName as keyof typeof fieldWeights];
      
      // Calculate term order preservation score
      const orderScore = this.calculateTermOrderScore(queryTerms, fieldText);
      
      // Calculate proximity bonus for terms within 10 words
      const proximityScore = this.calculateProximityScore(queryTerms, fieldText);
      
      // Combined score as per specification
      const combinedScore = (orderScore * 0.3 + proximityScore * 0.2) * fieldWeight;
      maxScore = Math.max(maxScore, combinedScore);
    }

    return maxScore;
  }

  /**
   * Calculate how well the order of query terms is preserved in the text
   * @returns Score between 0 and 1 indicating order preservation
   */
  private calculateTermOrderScore(queryTerms: string[], text: string): number {
    const normalizedText = text.toLowerCase();
    const positions: Map<string, number[]> = new Map();

    // Find all positions of each query term
    for (const term of queryTerms) {
      const termPositions: number[] = [];
      const normalizedTerm = term.toLowerCase();
      let pos = normalizedText.indexOf(normalizedTerm);
      
      while (pos !== -1) {
        termPositions.push(pos);
        pos = normalizedText.indexOf(normalizedTerm, pos + 1);
      }
      
      if (termPositions.length > 0) {
        positions.set(term, termPositions);
      }
    }

    // If not all terms are found, return 0
    if (positions.size < queryTerms.length) {
      return 0;
    }

    // Find the best ordered sequence
    let bestOrderScore = 0;

    // Try each combination of positions to find the best ordered sequence
    const findBestOrder = (termIndex: number, lastPos: number, orderCount: number): void => {
      if (termIndex >= queryTerms.length) {
        const score = orderCount / (queryTerms.length - 1);
        bestOrderScore = Math.max(bestOrderScore, score);
        return;
      }

      const term = queryTerms[termIndex];
      const termPositions = positions.get(term);
      
      if (!termPositions) return;

      for (const pos of termPositions) {
        let newOrderCount = orderCount;
        if (termIndex > 0 && pos > lastPos) {
          newOrderCount++;
        }
        findBestOrder(termIndex + 1, pos, newOrderCount);
      }
    };

    findBestOrder(0, -1, 0);
    return bestOrderScore;
  }

  /**
   * Calculate proximity score for terms appearing within 10 words of each other
   * @returns Score between 0 and 1 based on term proximity
   */
  private calculateProximityScore(queryTerms: string[], text: string): number {
    const normalizedText = text.toLowerCase();
    const words = normalizedText.split(/\s+/);
    
    // Find positions of query terms in word array
    const termPositions: Map<string, number[]> = new Map();
    
    for (const term of queryTerms) {
      const normalizedTerm = term.toLowerCase();
      const positions: number[] = [];
      
      for (let i = 0; i < words.length; i++) {
        if (words[i].includes(normalizedTerm)) {
          positions.push(i);
        }
      }
      
      if (positions.length > 0) {
        termPositions.set(term, positions);
      }
    }

    // If not enough terms found, return 0
    if (termPositions.size < 2) {
      return 0;
    }

    // Calculate proximity score based on terms within 10 words
    let proximityPairs = 0;
    let totalPairs = 0;

    const termsFound = Array.from(termPositions.keys());
    
    for (let i = 0; i < termsFound.length - 1; i++) {
      for (let j = i + 1; j < termsFound.length; j++) {
        const positions1 = termPositions.get(termsFound[i])!;
        const positions2 = termPositions.get(termsFound[j])!;
        
        // Check all position pairs
        for (const pos1 of positions1) {
          for (const pos2 of positions2) {
            totalPairs++;
            const distance = Math.abs(pos1 - pos2);
            
            // Terms within 10 words get proximity bonus
            if (distance <= 10) {
              // Closer terms get higher score (linear decay)
              const proximityScore = (10 - distance) / 10;
              proximityPairs += proximityScore;
            }
          }
        }
      }
    }

    return totalPairs > 0 ? proximityPairs / totalPairs : 0;
  }

  /**
   * TASK-2.4: Calculate exact match boost multiplier
   * Exact matches get 2x multiplier, case-insensitive exact matches get 1.5x
   * @returns Boost multiplier (1.0 for no exact match, 1.5-2.0 for exact matches)
   */
  private calculateExactMatchBoost(
    originalQuery: string,
    originalFields: {
      title: string;
      url: string;
      content: string;
    },
    normalizedFields: {
      title: string;
      url: string;
      content: string;
    }
  ): number {
    const normalizedQuery = this.normalizeString(originalQuery);
    
    // Check for exact match in each field
    for (const fieldName of ['title', 'url', 'content'] as const) {
      const originalField = originalFields[fieldName];
      const normalizedField = normalizedFields[fieldName];
      
      // First check for case-sensitive exact match
      if (originalField.includes(originalQuery)) {
        // Check if it's a complete match (not part of a larger string)
        const regex = new RegExp(`\\b${this.escapeRegex(originalQuery)}\\b`);
        if (regex.test(originalField)) {
          // Found case-sensitive exact match - return 2x multiplier
          return 2.0;
        }
      }
      
      // Then check for case-insensitive exact match
      if (normalizedField.includes(normalizedQuery)) {
        // Check if it's a complete match (not part of a larger string)
        const regex = new RegExp(`\\b${this.escapeRegex(normalizedQuery)}\\b`);
        if (regex.test(normalizedField)) {
          // Found case-insensitive exact match - return 1.5x multiplier
          return 1.5;
        }
      }
    }

    // No exact match found
    return 1.0;
  }

  /**
   * Escape special regex characters in a string
   */
  private escapeRegex(str: string): string {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Calculate Levenshtein-based similarity (optimized for performance)
   */
  private levenshteinSimilarity(s1: string, s2: string): number {
    const longer = s1.length > s2.length ? s1 : s2;
    const shorter = s1.length > s2.length ? s2 : s1;

    if (longer.length === 0) {
      return 1.0;
    }

    // For performance, use simple checks for very different lengths
    const lengthRatio = shorter.length / longer.length;
    if (lengthRatio < 0.3) {
      return shorter.length > 0 && longer.includes(shorter) ? 0.5 : 0;
    }

    // If strings are very long, use a faster approximation
    if (longer.length > 200) {
      return longer.includes(shorter) ? 0.7 : 0;
    }

    const editDistance = this.levenshteinDistance(shorter, longer);
    return (longer.length - editDistance) / longer.length;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(s1: string, s2: string): number {
    const matrix: number[][] = [];

    for (let i = 0; i <= s2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= s1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= s2.length; i++) {
      for (let j = 1; j <= s1.length; j++) {
        if (s2.charAt(i - 1) === s1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          );
        }
      }
    }

    return matrix[s2.length][s1.length];
  }

  /**
   * Generate snippet from content with query term highlights
   */
  private generateSnippet(content: string, queryTerms: string[], maxLength: number = 200): string {
    if (!content || queryTerms.length === 0) {
      return content.substring(0, maxLength);
    }

    const normalizedContent = this.normalizeString(content);
    
    // Find the best position that contains most query terms
    let bestPosition = 0;
    let maxTermCount = 0;

    for (let i = 0; i < normalizedContent.length - maxLength; i += 20) {
      const window = normalizedContent.substring(i, i + maxLength);
      const termCount = queryTerms.filter(term => window.includes(term)).length;
      
      if (termCount > maxTermCount) {
        maxTermCount = termCount;
        bestPosition = i;
      }
    }

    const snippet = content.substring(bestPosition, bestPosition + maxLength);
    return snippet + (bestPosition + maxLength < content.length ? '...' : '');
  }

  /**
   * Generate highlight terms found in the page
   */
  private generateHighlights(page: Page, queryTerms: string[]): string[] {
    const highlights: Set<string> = new Set();
    const fields = [page.title, page.url, page.content];

    for (const field of fields) {
      const normalized = this.normalizeString(field);
      for (const term of queryTerms) {
        if (normalized.includes(term)) {
          highlights.add(term);
        }
      }
    }

    return Array.from(highlights);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<StringMatcherConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): StringMatcherConfig {
    return { ...this.config };
  }
}

/**
 * Default StringMatcher instance for convenience
 */
export const defaultStringMatcher = new StringMatcher();

/**
 * Convenience function for string-based search
 */
export function stringSearch(query: string, pages: Page[], config?: StringMatcherConfig): SearchResult[] {
  const matcher = config ? new StringMatcher(config) : defaultStringMatcher;
  return matcher.search(query, pages);
}