/**
 * 混合搜索服务
 * 
 * 整合 HybridSearchEngine 的服务层封装，提供统一的搜索接口
 */

import { HybridSearchEngine } from '../search/hybrid/HybridSearchEngine';
import type { Page, SearchOptions, SearchResultItem } from '../models';
import { dbService } from './db.service';
import { searchLogger as logger } from '../utils';

/**
 * 搜索结果
 */
export interface SearchResult {
  results: SearchResultItem[];
  totalResults: number;
  searchTime: number;
  searchMode: 'hybrid' | 'fuse' | 'fulltext';
  metadata?: {
    processingSteps: string[];
    fallbackUsed?: boolean;
    stringResults?: number;
    fulltextResults?: number;
  };
}

/**
 * 混合搜索服务类
 */
export class HybridSearchService {
  private static instance: HybridSearchService | null = null;
  private hybridEngine: HybridSearchEngine;
  private pages: Page[] = [];
  private isIndexing: boolean = false;
  private lastIndexUpdate: number = 0;
  
  // Suggestion caching
  private suggestionCache = new Map<string, { suggestions: string[]; timestamp: number }>();
  private readonly suggestionCacheExpiry = 5 * 60 * 1000; // 5 minutes
  private readonly maxCacheSize = 100;

  private constructor() {
    this.hybridEngine = new HybridSearchEngine();
  }

  /**
   * 获取服务实例（单例模式）
   */
  public static getInstance(): HybridSearchService {
    if (!HybridSearchService.instance) {
      HybridSearchService.instance = new HybridSearchService();
    }
    return HybridSearchService.instance;
  }

  /**
   * 初始化搜索服务
   */
  public async init(): Promise<void> {
    try {
      await this.buildIndex();
      logger.info('Hybrid search service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize hybrid search service', error);
      throw error;
    }
  }

  /**
   * 构建搜索索引
   */
  public async buildIndex(): Promise<void> {
    if (this.isIndexing) {
      logger.info('Index building already in progress');
      return;
    }

    this.isIndexing = true;

    try {
      logger.info('Building hybrid search index...');
      
      // 首先加载最近的页面用于即时搜索
      const initialResult = await dbService.getPagesPaginated({
        limit: 200,
        sortBy: 'visitTime',
        sortOrder: 'desc'
      });
      
      this.pages = initialResult.items;
      
      // 初始化混合搜索引擎的索引
      await this.hybridEngine.initialize(this.pages);
      
      this.lastIndexUpdate = Date.now();
      
      // Clean expired suggestion cache
      this.cleanExpiredSuggestionCache();
      
      logger.info(`Initial hybrid search index built with ${this.pages.length} pages`);
      
      // 如果还有更多页面，在后台继续加载
      if (initialResult.hasMore) {
        this.loadRemainingPagesInBackground(initialResult.nextCursor);
      }
      
    } catch (error) {
      logger.error('Failed to build hybrid search index', error);
      throw error;
    } finally {
      this.isIndexing = false;
    }
  }

  /**
   * 在后台加载剩余页面
   */
  private async loadRemainingPagesInBackground(cursor: string | null): Promise<void> {
    try {
      let currentCursor = cursor;
      let batchCount = 0;
      
      while (currentCursor) {
        // 使用 requestIdleCallback 避免阻塞主线程
        await new Promise(resolve => {
          if ('requestIdleCallback' in window) {
            requestIdleCallback(() => resolve(undefined), { timeout: 100 });
          } else {
            setTimeout(resolve, 10);
          }
        });
        
        const result = await dbService.getPagesPaginated({
          limit: 500,
          cursor: currentCursor,
          sortBy: 'visitTime',
          sortOrder: 'desc'
        });
        
        if (result.items.length > 0) {
          // 添加到现有页面数组
          this.pages = [...this.pages, ...result.items];
          
          // 重建索引
          await this.hybridEngine.initialize(this.pages);
          
          batchCount++;
          logger.info(`Background index update: loaded batch ${batchCount}, total pages: ${this.pages.length}`);
        }
        
        currentCursor = result.hasMore ? result.nextCursor : null;
      }
      
      logger.info(`Background index loading completed. Total pages: ${this.pages.length}`);
      
    } catch (error) {
      logger.error('Error loading remaining pages in background', error);
    }
  }

  /**
   * 执行搜索
   */
  public async search(query: string, options: Partial<SearchOptions> = {}): Promise<SearchResultItem[]> {
    if (!query || query.trim().length === 0) {
      return [];
    }

    const result = await this.searchEnhanced(query, options);
    return result.results;
  }

  /**
   * 执行增强搜索
   */
  public async searchEnhanced(query: string, options: Partial<SearchOptions> = {}): Promise<SearchResult> {
    const startTime = performance.now();
    const processingSteps: string[] = [];

    if (!query || query.trim().length === 0) {
      return {
        results: [],
        totalResults: 0,
        searchTime: 0,
        searchMode: 'hybrid',
        metadata: { processingSteps: ['Empty query'] }
      };
    }

    try {
      processingSteps.push('Query received and validated');
      processingSteps.push('Using hybrid search engine');
      
      // 使用混合搜索引擎
      const hybridResults = await this.hybridEngine.search(query);
      
      // 转换结果格式
      const searchResultItems = this.convertToSearchResultItems(hybridResults.mergedResults);
      
      // 应用额外的过滤和排序选项
      let finalResults = this.applyOptions(searchResultItems, options);
      
      const searchTime = performance.now() - startTime;

      logger.info(`Hybrid search completed in ${searchTime.toFixed(2)}ms`, {
        mode: 'hybrid',
        totalResults: finalResults.length,
        stringResults: hybridResults.stringResults.length,
        fulltextResults: hybridResults.fulltextResults.length
      });

      return {
        results: finalResults,
        totalResults: finalResults.length,
        searchTime,
        searchMode: 'hybrid',
        metadata: { 
          processingSteps,
          stringResults: hybridResults.stringResults.length,
          fulltextResults: hybridResults.fulltextResults.length
        }
      };

    } catch (error) {
      logger.error('Hybrid search failed', error);
      processingSteps.push(`Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      const searchTime = performance.now() - startTime;
      
      return {
        results: [],
        totalResults: 0,
        searchTime,
        searchMode: 'hybrid',
        metadata: {
          processingSteps,
          fallbackUsed: true
        }
      };
    }
  }

  /**
   * 获取搜索建议
   */
  public async getSuggestions(query: string, limit: number = 5): Promise<string[]> {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const queryKey = `${query.toLowerCase().trim()}_${limit}`;
    
    // Check cache first
    const cached = this.suggestionCache.get(queryKey);
    if (cached && (Date.now() - cached.timestamp) < this.suggestionCacheExpiry) {
      return cached.suggestions;
    }

    try {
      const suggestions = new Map<string, number>();
      const queryLower = query.toLowerCase();
      
      // Extract suggestions from page titles and content
      const maxPagesToProcess = Math.min(this.pages.length, 1000);
      const pagesToProcess = this.pages.slice(0, maxPagesToProcess);
      
      for (const page of pagesToProcess) {
        // From title
        const titleWords = page.title.toLowerCase().split(/\s+/).slice(0, 20);
        for (const word of titleWords) {
          if (word.length >= 3 && word.startsWith(queryLower) && word.length > queryLower.length) {
            const priority = suggestions.get(word) || 0;
            suggestions.set(word, Math.max(priority, 100));
          }
        }
        
        // From URL
        try {
          const urlParts = new URL(page.url).pathname.toLowerCase().split(/[/\-_.]/);
          for (const part of urlParts.slice(0, 10)) {
            if (part.length >= 3 && part.startsWith(queryLower) && part.length > queryLower.length) {
              const priority = suggestions.get(part) || 0;
              suggestions.set(part, Math.max(priority, 50));
            }
          }
        } catch {
          // Ignore invalid URLs
        }
      }
      
      // Sort and return top suggestions
      const sortedSuggestions = Array.from(suggestions.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([word]) => word)
        .slice(0, limit);
      
      // Cache the results
      this.cacheSuggestion(queryKey, sortedSuggestions);
      
      return sortedSuggestions;
      
    } catch (error) {
      logger.error('Failed to get suggestions', error);
      return [];
    }
  }

  /**
   * 将 HybridSearchEngine 的 SearchResult 转换为 SearchResultItem
   */
  private convertToSearchResultItems(results: any[]): SearchResultItem[] {
    return results.map(result => ({
      page: {
        id: result.id,
        url: result.url,
        title: result.title,
        content: result.content,
        domain: result.domain || '',
        visitTime: result.lastVisitTime,
        lastUpdated: result.lastVisitTime, // Use visitTime as lastUpdated
        accessCount: result.visitCount,
        language: result.language || 'en'
      },
      score: result.combinedScore,
      highlights: result.highlights || [],
      matchedFields: [] // TODO: Extract from result metadata
    }));
  }

  /**
   * 应用搜索选项
   */
  private applyOptions(results: SearchResultItem[], options: Partial<SearchOptions>): SearchResultItem[] {
    let filtered = [...results];

    // 时间范围过滤
    if (options.timeRange) {
      filtered = filtered.filter(result => 
        result.page.visitTime >= options.timeRange!.start && 
        result.page.visitTime <= options.timeRange!.end
      );
    }

    // 域名过滤
    if (options.domains && options.domains.length > 0) {
      filtered = filtered.filter(result => 
        options.domains!.includes(result.page.domain)
      );
    }

    // 排除域名
    if (options.excludeDomains && options.excludeDomains.length > 0) {
      filtered = filtered.filter(result => 
        !options.excludeDomains!.includes(result.page.domain)
      );
    }

    // 排序
    if (options.sortBy) {
      filtered.sort((a, b) => {
        switch (options.sortBy) {
          case 'time':
            return options.sortOrder === 'desc' 
              ? b.page.visitTime - a.page.visitTime 
              : a.page.visitTime - b.page.visitTime;
          case 'accessCount':
            return options.sortOrder === 'desc'
              ? b.page.accessCount - a.page.accessCount
              : a.page.accessCount - b.page.accessCount;
          case 'relevance':
          default:
            return options.sortOrder === 'desc'
              ? a.score - b.score
              : b.score - a.score;
        }
      });
    }

    // 限制结果数量
    if (options.limit) {
      filtered = filtered.slice(0, options.limit);
    }

    return filtered;
  }

  /**
   * 缓存建议结果
   */
  private cacheSuggestion(key: string, suggestions: string[]): void {
    if (this.suggestionCache.size >= this.maxCacheSize) {
      const oldestKey = this.suggestionCache.keys().next().value;
      if (oldestKey) {
        this.suggestionCache.delete(oldestKey);
      }
    }
    
    this.suggestionCache.set(key, {
      suggestions,
      timestamp: Date.now()
    });
  }

  /**
   * 清理过期的建议缓存
   */
  private cleanExpiredSuggestionCache(): void {
    const now = Date.now();
    for (const [key, value] of this.suggestionCache.entries()) {
      if (now - value.timestamp > this.suggestionCacheExpiry) {
        this.suggestionCache.delete(key);
      }
    }
  }

  /**
   * 获取索引统计信息
   */
  public getIndexStats(): {
    totalPages: number;
    lastUpdate: number;
    isIndexing: boolean;
    engineType: string;
  } {
    return {
      totalPages: this.pages.length,
      lastUpdate: this.lastIndexUpdate,
      isIndexing: this.isIndexing,
      engineType: 'hybrid'
    };
  }

  /**
   * 检查是否需要重建索引
   */
  public async shouldRebuildIndex(): Promise<boolean> {
    try {
      const storageInfo = await dbService.getStorageInfo();
      
      // 如果页面数量发生变化，需要重建
      if (storageInfo.pagesCount !== this.pages.length) {
        return true;
      }
      
      // 如果超过一定时间，需要重建
      const oneHour = 60 * 60 * 1000;
      if (Date.now() - this.lastIndexUpdate > oneHour) {
        return true;
      }
      
      return false;
      
    } catch (error) {
      logger.error('Failed to check if index rebuild is needed', error);
      return true;
    }
  }

  /**
   * 清理搜索索引
   */
  public clearIndex(): void {
    this.pages = [];
    this.lastIndexUpdate = 0;
    this.hybridEngine = new HybridSearchEngine();
    
    logger.info('Hybrid search index cleared');
  }

  /**
   * 添加页面到索引
   */
  public async addPageToIndex(page: Page): Promise<void> {
    try {
      // 添加到页面列表
      const existingIndex = this.pages.findIndex(p => p.id === page.id);
      if (existingIndex >= 0) {
        this.pages[existingIndex] = page;
      } else {
        this.pages.push(page);
      }
      
      // 重建索引
      await this.hybridEngine.updateIndex(this.pages);
      
      logger.info(`Page "${page.title}" added to hybrid search index`);
    } catch (error) {
      logger.error('Failed to add page to index', error);
    }
  }

  /**
   * 从索引中移除页面
   */
  public async removePageFromIndex(pageId: string): Promise<void> {
    try {
      // 从页面列表中移除
      this.pages = this.pages.filter(p => p.id !== pageId);
      
      // 重建索引
      await this.hybridEngine.updateIndex(this.pages);
      
      logger.info(`Page ${pageId} removed from hybrid search index`);
    } catch (error) {
      logger.error('Failed to remove page from index', error);
    }
  }

  /**
   * 重置服务实例（主要用于测试）
   */
  public static reset(): void {
    if (HybridSearchService.instance) {
      HybridSearchService.instance.clearIndex();
      HybridSearchService.instance = null;
    }
  }
}

// 导出单例实例
export const hybridSearchService = HybridSearchService.getInstance();