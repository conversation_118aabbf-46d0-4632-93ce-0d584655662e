/**
 * IndexedDB 服务模块
 * 
 * 提供对 Recall 数据库的完整 CRUD 操作
 * 支持页面数据和用户设置的管理
 */

import type { Page, PaginationOptions, PageFilter, PaginatedResult } from '../models';
import {
  DB_CONFIG,
  DBError,
  DB_ERROR_CODES,
  validatePage,
  createPage,
  updatePage,
  createSetting,
  updateSetting
} from '../models';

/**
 * IndexedDB 服务类
 * 使用单例模式确保数据库连接的唯一性
 */
export class IndexedDBService {
  private static instance: IndexedDBService | null = null;
  private db: IDBDatabase | null = null;
  private initPromise: Promise<void> | null = null;

  private constructor() {}

  /**
   * 获取服务实例（单例模式）
   */
  public static getInstance(): IndexedDBService {
    if (!IndexedDBService.instance) {
      IndexedDBService.instance = new IndexedDBService();
    }
    return IndexedDBService.instance;
  }

  /**
   * 初始化数据库
   */
  public async init(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.initializeDatabase();
    return this.initPromise;
  }

  /**
   * 初始化数据库的具体实现
   */
  private async initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check for IndexedDB in both browser and service worker environments
      const globalScope = typeof window !== 'undefined' ? window : globalThis;
      if (!('indexedDB' in globalScope)) {
        reject(new DBError(
          'IndexedDB is not supported in this environment',
          DB_ERROR_CODES.DB_NOT_AVAILABLE
        ));
        return;
      }

      const request = globalScope.indexedDB.open(DB_CONFIG.name, DB_CONFIG.version);

      request.onerror = () => {
        reject(new DBError(
          'Failed to open database',
          DB_ERROR_CODES.DB_NOT_AVAILABLE,
          request.error || undefined
        ));
      };

      request.onsuccess = () => {
        this.db = request.result;
        
        // 处理数据库意外关闭
        this.db.onclose = () => {
          console.warn('Database connection closed unexpectedly');
          this.db = null;
          this.initPromise = null;
        };

        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        try {
          this.createObjectStores(db);
        } catch (error) {
          reject(new DBError(
            'Failed to create object stores',
            DB_ERROR_CODES.TRANSACTION_FAILED,
            error as Error
          ));
        }
      };
    });
  }

  /**
   * 创建对象存储和索引
   */
  private createObjectStores(db: IDBDatabase): void {
    // 创建 pages 对象存储
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.pages)) {
      const pagesStore = db.createObjectStore(DB_CONFIG.stores.pages, {
        keyPath: 'id'
      });

      // 创建索引
      pagesStore.createIndex(DB_CONFIG.indexes.pages.url, 'url', { unique: true });
      pagesStore.createIndex(DB_CONFIG.indexes.pages.domain, 'domain', { unique: false });
      pagesStore.createIndex(DB_CONFIG.indexes.pages.visitTime, 'visitTime', { unique: false });
      pagesStore.createIndex(DB_CONFIG.indexes.pages.lastUpdated, 'lastUpdated', { unique: false });
    }

    // 创建 settings 对象存储
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.settings)) {
      db.createObjectStore(DB_CONFIG.stores.settings, {
        keyPath: 'key'
      });
    }

    // 创建 blacklist 对象存储
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.blacklist)) {
      const blacklistStore = db.createObjectStore(DB_CONFIG.stores.blacklist, {
        keyPath: 'domain'
      });

      // 创建索引
      blacklistStore.createIndex(DB_CONFIG.indexes.blacklist.createdAt, 'createdAt', { unique: false });
    }
  }

  /**
   * 确保数据库已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.db) {
      await this.init();
    }
    
    if (!this.db) {
      throw new DBError(
        'Database is not available',
        DB_ERROR_CODES.DB_NOT_AVAILABLE
      );
    }
  }

  /**
   * 创建事务
   */
  private createTransaction(
    storeNames: string | string[], 
    mode: IDBTransactionMode = 'readonly'
  ): IDBTransaction {
    if (!this.db) {
      throw new DBError(
        'Database is not initialized',
        DB_ERROR_CODES.DB_NOT_AVAILABLE
      );
    }

    return this.db.transaction(storeNames, mode);
  }

  /**
   * 执行事务操作
   */
  private async executeTransaction<T>(
    operation: (transaction: IDBTransaction) => Promise<T>
  ): Promise<T> {
    await this.ensureInitialized();
    
    try {
      const transaction = this.createTransaction(
        [DB_CONFIG.stores.pages, DB_CONFIG.stores.settings], 
        'readwrite'
      );
      
      return await operation(transaction);
    } catch (error) {
      throw new DBError(
        'Transaction failed',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Promise 包装器，用于 IndexedDB 请求
   */
  private promiseRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // ==================== Pages 表操作 ====================

  /**
   * 添加页面记录
   */
  public async addPage(pageData: {
    url: string;
    title: string;
    content: string;
  }): Promise<Page> {
    const validation = validatePage(pageData);
    if (!validation.valid) {
      throw new DBError(
        `Invalid page data: ${validation.errors.join(', ')}`,
        DB_ERROR_CODES.TRANSACTION_FAILED
      );
    }

    const page = createPage(pageData);

    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.pages);
      
      // 检查URL是否已存在
      const urlIndex = store.index(DB_CONFIG.indexes.pages.url);
      const existingPage = await this.promiseRequest(urlIndex.get(page.url));
      
      if (existingPage) {
        // 如果页面已存在，更新访问次数和内容
        const updatedPage = updatePage(existingPage, {
          title: page.title,
          content: page.content
        });
        
        await this.promiseRequest(store.put(updatedPage));
        return updatedPage;
      } else {
        // 添加新页面
        await this.promiseRequest(store.add(page));
        return page;
      }
    });
  }

  /**
   * 添加页面记录（支持状态信息的版本）
   */
  public async addPageWithStatus(pageData: {
    url: string;
    title: string;
    content: string;
    contentStatus?: 'extracted' | 'failed' | 'pending' | 'empty';
    extractionError?: string;
    lastExtractionAttempt?: number;
  }): Promise<Page> {
    const validation = validatePage(pageData);
    if (!validation.valid) {
      throw new DBError(
        `Invalid page data: ${validation.errors.join(', ')}`,
        DB_ERROR_CODES.VALIDATION_FAILED
      );
    }

    const page = createPage(pageData);

    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.pages);
      
      // 检查URL是否已存在
      const urlIndex = store.index(DB_CONFIG.indexes.pages.url);
      const existingPage = await this.promiseRequest(urlIndex.get(page.url));
      
      if (existingPage) {
        // 如果页面已存在，更新访问次数和内容状态
        const updatedPage = updatePage(existingPage, {
          title: page.title,
          content: page.content,
          contentStatus: pageData.contentStatus,
          extractionError: pageData.extractionError,
          lastExtractionAttempt: pageData.lastExtractionAttempt
        });
        
        await this.promiseRequest(store.put(updatedPage));
        return updatedPage;
      } else {
        // 添加新页面
        await this.promiseRequest(store.add(page));
        return page;
      }
    });
  }

  /**
   * 添加页面记录（用于导入，保留原始时间戳）
   */
  public async addPageForImport(pageData: {
    url: string;
    title: string;
    content: string;
    visitTime?: number;
    accessCount?: number;
    lastUpdated?: number;
    contentStatus?: 'extracted' | 'failed' | 'pending' | 'empty';
    extractionError?: string;
    lastExtractionAttempt?: number;
  }): Promise<Page> {
    const validation = validatePage(pageData);
    if (!validation.valid) {
      throw new DBError(
        `Invalid page data: ${validation.errors.join(', ')}`,
        DB_ERROR_CODES.VALIDATION_FAILED
      );
    }

    const page = createPage({
      ...pageData,
      visitTime: pageData.visitTime, // Preserve original visitTime
      accessCount: pageData.accessCount, // Preserve original accessCount
      lastUpdated: pageData.lastUpdated // Preserve original lastUpdated
    });

    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.pages);
      
      // 检查URL是否已存在
      const urlIndex = store.index(DB_CONFIG.indexes.pages.url);
      const existingPage = await this.promiseRequest(urlIndex.get(page.url));
      
      if (existingPage) {
        // 如果页面已存在，保留原始时间戳进行更新
        const updatedPage = {
          ...existingPage,
          title: page.title,
          content: page.content,
          visitTime: pageData.visitTime || existingPage.visitTime, // Preserve import time if available
          accessCount: Math.max(pageData.accessCount || 1, existingPage.accessCount), // Keep highest count
          lastUpdated: pageData.lastUpdated || Date.now(), // Use import time or current
          contentStatus: pageData.contentStatus || existingPage.contentStatus,
          extractionError: pageData.extractionError !== undefined ? pageData.extractionError : existingPage.extractionError,
          lastExtractionAttempt: pageData.lastExtractionAttempt || existingPage.lastExtractionAttempt
        };
        
        await this.promiseRequest(store.put(updatedPage));
        return updatedPage;
      } else {
        // 添加新页面，保留原始时间戳
        await this.promiseRequest(store.add(page));
        return page;
      }
    });
  }

  /**
   * 根据ID获取页面
   */
  public async getPage(id: string): Promise<Page | null> {
    await this.ensureInitialized();
    
    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);
    
    try {
      const result = await this.promiseRequest(store.get(id));
      return result || null;
    } catch (error) {
      throw new DBError(
        'Failed to get page',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 根据URL获取页面
   */
  public async getPageByUrl(url: string): Promise<Page | null> {
    await this.ensureInitialized();
    
    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);
    const index = store.index(DB_CONFIG.indexes.pages.url);
    
    try {
      const result = await this.promiseRequest(index.get(url));
      return result || null;
    } catch (error) {
      throw new DBError(
        'Failed to get page by URL',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 更新页面
   */
  public async updatePage(id: string, updates: Partial<Page>): Promise<Page> {
    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.pages);
      
      const existingPage = await this.promiseRequest(store.get(id));
      if (!existingPage) {
        throw new DBError(
          'Page not found',
          DB_ERROR_CODES.ITEM_NOT_FOUND
        );
      }

      const updatedPage = { ...existingPage, ...updates, lastUpdated: Date.now() };
      await this.promiseRequest(store.put(updatedPage));
      
      return updatedPage;
    });
  }

  /**
   * 删除页面
   */
  public async deletePage(id: string): Promise<boolean> {
    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.pages);
      
      const existingPage = await this.promiseRequest(store.get(id));
      if (!existingPage) {
        return false;
      }

      await this.promiseRequest(store.delete(id));
      return true;
    });
  }

  /**
   * 获取所有页面
   */
  public async getAllPages(): Promise<Page[]> {
    await this.ensureInitialized();

    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);

    try {
      const result = await this.promiseRequest(store.getAll());
      return result;
    } catch (error) {
      throw new DBError(
        'Failed to get all pages',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 分页获取页面数据（支持虚拟滚动）
   * @param options 分页选项
   * @returns 分页结果
   */
  public async getPagesPaginated(options: PaginationOptions = {}): Promise<PaginatedResult<Page>> {
    await this.ensureInitialized();

    const {
      limit = 50,
      cursor = null,
      sortBy = 'visitTime',
      sortOrder = 'desc',
      filter = null
    } = options;

    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);

    try {
      // Determine which index to use based on sortBy
      let source: IDBObjectStore | IDBIndex;
      if (sortBy === 'visitTime' || sortBy === 'lastUpdated') {
        source = store.index(DB_CONFIG.indexes.pages[sortBy]);
      } else if (sortBy === 'domain') {
        source = store.index(DB_CONFIG.indexes.pages.domain);
      } else {
        // Default to primary key (id) for other cases
        source = store;
      }

      const direction = sortOrder === 'desc' ? 'prev' : 'next';
      const items: Page[] = [];
      let hasMore = false;
      let nextCursor: string | null = null;

      return new Promise((resolve, reject) => {
        const request = cursor ? source.openCursor(null, direction) : source.openCursor(null, direction);

        // If we have a cursor, advance to that position
        const skipToPosition = cursor ? true : false;
        let skipped = false;

        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;

          if (!cursor) {
            // No more items
            resolve({
              items,
              hasMore: false,
              nextCursor: null,
              totalCount: items.length // This is approximate for performance
            });
            return;
          }

          // Skip to cursor position if needed
          if (skipToPosition && !skipped) {
            if (cursor.primaryKey.toString() === options.cursor) {
              skipped = true;
              cursor.continue();
              return;
            } else {
              cursor.continue();
              return;
            }
          }

          const page = cursor.value as Page;

          // Apply filter if provided
          if (filter && !this.matchesFilter(page, filter)) {
            cursor.continue();
            return;
          }

          // Check if we've reached the limit
          if (items.length >= limit) {
            hasMore = true;
            nextCursor = cursor.primaryKey.toString();
            resolve({
              items,
              hasMore,
              nextCursor,
              totalCount: items.length + 1 // Approximate
            });
            return;
          }

          items.push(page);
          cursor.continue();
        };

        request.onerror = () => {
          reject(new DBError(
            'Failed to get paginated pages',
            DB_ERROR_CODES.TRANSACTION_FAILED,
            request.error || undefined
          ));
        };
      });
    } catch (error) {
      throw new DBError(
        'Failed to get paginated pages',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 获取页面总数（用于虚拟滚动计算）
   */
  public async getPageCount(filter?: PageFilter): Promise<number> {
    await this.ensureInitialized();

    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);

    try {
      if (!filter) {
        // Simple count without filter
        const result = await this.promiseRequest(store.count());
        return result;
      }

      // Count with filter (requires iteration)
      return new Promise((resolve, reject) => {
        let count = 0;
        const request = store.openCursor();

        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;

          if (!cursor) {
            resolve(count);
            return;
          }

          const page = cursor.value as Page;
          if (this.matchesFilter(page, filter)) {
            count++;
          }

          cursor.continue();
        };

        request.onerror = () => {
          reject(new DBError(
            'Failed to count pages',
            DB_ERROR_CODES.TRANSACTION_FAILED,
            request.error || undefined
          ));
        };
      });
    } catch (error) {
      throw new DBError(
        'Failed to count pages',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  // ==================== Public Helper Methods ====================

  /**
   * 公共方法：确保数据库已初始化
   */
  public async ensureReady(): Promise<void> {
    await this.ensureInitialized();
  }

  /**
   * 公共方法：创建事务
   */
  public createPublicTransaction(
    storeNames: string | string[],
    mode: IDBTransactionMode = 'readonly'
  ): IDBTransaction {
    return this.createTransaction(storeNames, mode);
  }

  /**
   * 公共方法：Promise 包装器
   */
  public promiseRequestPublic<T>(request: IDBRequest<T>): Promise<T> {
    return this.promiseRequest(request);
  }

  /**
   * 检查页面是否匹配过滤条件
   */
  private matchesFilter(page: Page, filter: PageFilter): boolean {
    // Domain filter
    if (filter.domains && filter.domains.length > 0) {
      if (!filter.domains.includes(page.domain)) {
        return false;
      }
    }

    // Exclude domains filter
    if (filter.excludeDomains && filter.excludeDomains.length > 0) {
      if (filter.excludeDomains.includes(page.domain)) {
        return false;
      }
    }

    // Time range filter
    if (filter.timeRange) {
      if (page.visitTime < filter.timeRange.start || page.visitTime > filter.timeRange.end) {
        return false;
      }
    }

    // Search query filter (simple text matching)
    if (filter.searchQuery) {
      const query = filter.searchQuery.toLowerCase();
      const searchText = `${page.title} ${page.content} ${page.url}`.toLowerCase();
      if (!searchText.includes(query)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 根据域名获取页面
   */
  public async getPagesByDomain(domain: string): Promise<Page[]> {
    await this.ensureInitialized();

    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);
    const index = store.index(DB_CONFIG.indexes.pages.domain);

    try {
      const result = await this.promiseRequest(index.getAll(domain));
      return result;
    } catch (error) {
      throw new DBError(
        'Failed to get pages by domain',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 根据时间范围获取页面
   */
  public async getPagesByTimeRange(startTime: number, endTime: number): Promise<Page[]> {
    await this.ensureInitialized();

    const transaction = this.createTransaction(DB_CONFIG.stores.pages);
    const store = transaction.objectStore(DB_CONFIG.stores.pages);
    const index = store.index(DB_CONFIG.indexes.pages.visitTime);

    try {
      const range = IDBKeyRange.bound(startTime, endTime);
      const result = await this.promiseRequest(index.getAll(range));
      return result;
    } catch (error) {
      throw new DBError(
        'Failed to get pages by time range',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 批量删除页面
   */
  public async deletePages(ids: string[]): Promise<number> {
    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.pages);
      let deletedCount = 0;

      for (const id of ids) {
        try {
          await this.promiseRequest(store.delete(id));
          deletedCount++;
        } catch (error) {
          console.warn(`Failed to delete page ${id}:`, error);
        }
      }

      return deletedCount;
    });
  }

  // ==================== Settings 表操作 ====================

  /**
   * 获取设置
   */
  public async getSetting(key: string): Promise<any> {
    await this.ensureInitialized();

    const transaction = this.createTransaction(DB_CONFIG.stores.settings);
    const store = transaction.objectStore(DB_CONFIG.stores.settings);

    try {
      const result = await this.promiseRequest(store.get(key));
      return result ? result.value : null;
    } catch (error) {
      throw new DBError(
        'Failed to get setting',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 设置值
   */
  public async setSetting(key: string, value: any): Promise<void> {
    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.settings);

      const existingSetting = await this.promiseRequest(store.get(key));

      if (existingSetting) {
        const updatedSetting = updateSetting(existingSetting, value);
        await this.promiseRequest(store.put(updatedSetting));
      } else {
        const newSetting = createSetting(key, value);
        await this.promiseRequest(store.add(newSetting));
      }
    });
  }

  /**
   * 删除设置
   */
  public async deleteSetting(key: string): Promise<boolean> {
    return this.executeTransaction(async (transaction) => {
      const store = transaction.objectStore(DB_CONFIG.stores.settings);

      const existingSetting = await this.promiseRequest(store.get(key));
      if (!existingSetting) {
        return false;
      }

      await this.promiseRequest(store.delete(key));
      return true;
    });
  }

  /**
   * 获取所有设置
   */
  public async getAllSettings(): Promise<Record<string, any>> {
    await this.ensureInitialized();

    const transaction = this.createTransaction(DB_CONFIG.stores.settings);
    const store = transaction.objectStore(DB_CONFIG.stores.settings);

    try {
      const settings = await this.promiseRequest(store.getAll());
      const result: Record<string, any> = {};

      for (const setting of settings) {
        result[setting.key] = setting.value;
      }

      return result;
    } catch (error) {
      throw new DBError(
        'Failed to get all settings',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  // ==================== 工具方法 ====================

  /**
   * 获取数据库存储信息
   */
  public async getStorageInfo(): Promise<{
    pagesCount: number;
    settingsCount: number;
    estimatedSize: number;
  }> {
    await this.ensureInitialized();

    const transaction = this.createTransaction([DB_CONFIG.stores.pages, DB_CONFIG.stores.settings]);
    const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
    const settingsStore = transaction.objectStore(DB_CONFIG.stores.settings);

    try {
      const [pagesCount, settingsCount] = await Promise.all([
        this.promiseRequest(pagesStore.count()),
        this.promiseRequest(settingsStore.count())
      ]);

      // 估算存储大小（简单估算）
      const estimatedSize = pagesCount * 5000 + settingsCount * 100; // 平均每页5KB，每设置100B

      return {
        pagesCount,
        settingsCount,
        estimatedSize
      };
    } catch (error) {
      throw new DBError(
        'Failed to get storage info',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * 清空所有数据
   */
  public async clearAllData(): Promise<void> {
    return this.executeTransaction(async (transaction) => {
      const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
      const settingsStore = transaction.objectStore(DB_CONFIG.stores.settings);

      await Promise.all([
        this.promiseRequest(pagesStore.clear()),
        this.promiseRequest(settingsStore.clear())
      ]);
    });
  }

  /**
   * 导出数据
   */
  public async exportData(): Promise<{
    pages: Page[];
    settings: Record<string, any>;
    exportTime: number;
    version: string;
  }> {
    const [pages, settings] = await Promise.all([
      this.getAllPages(),
      this.getAllSettings()
    ]);

    return {
      pages,
      settings,
      exportTime: Date.now(),
      version: '1.0.0'
    };
  }

  /**
   * 导入数据
   */
  public async importData(data: {
    pages: Page[];
    settings: Record<string, any>;
  }): Promise<void> {
    return this.executeTransaction(async (transaction) => {
      const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
      const settingsStore = transaction.objectStore(DB_CONFIG.stores.settings);

      // 导入页面数据
      for (const page of data.pages) {
        await this.promiseRequest(pagesStore.put(page));
      }

      // 导入设置数据
      for (const [key, value] of Object.entries(data.settings)) {
        const setting = createSetting(key, value);
        await this.promiseRequest(settingsStore.put(setting));
      }
    });
  }

  /**
   * 数据库健康检查
   */
  public async healthCheck(): Promise<{
    isHealthy: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];

    try {
      await this.ensureInitialized();

      if (!this.db) {
        issues.push('Database connection is not available');
      }

      // 检查对象存储是否存在
      if (this.db && !this.db.objectStoreNames.contains(DB_CONFIG.stores.pages)) {
        issues.push('Pages object store is missing');
      }

      if (this.db && !this.db.objectStoreNames.contains(DB_CONFIG.stores.settings)) {
        issues.push('Settings object store is missing');
      }

      // 尝试读取数据
      try {
        await this.getStorageInfo();
      } catch (error) {
        issues.push('Failed to read storage info');
      }

    } catch (error) {
      issues.push(`Database initialization failed: ${(error as Error).message}`);
    }

    return {
      isHealthy: issues.length === 0,
      issues
    };
  }

  /**
   * 关闭数据库连接
   */
  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.initPromise = null;
    }
  }

  /**
   * 重置服务实例（主要用于测试）
   */
  public static reset(): void {
    if (IndexedDBService.instance) {
      IndexedDBService.instance.close();
      IndexedDBService.instance = null;
    }
  }
}

// 导出单例实例
export const dbService = IndexedDBService.getInstance();
