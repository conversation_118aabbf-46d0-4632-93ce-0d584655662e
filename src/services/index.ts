/**
 * 服务模块导出
 *
 * 统一导出所有服务类和实例
 */

// 导出数据库服务
export { IndexedDBService, dbService } from './db.service';

// 导出搜索服务
export { SearchService, searchService } from './search.service';
export { HybridSearchService, hybridSearchService } from './hybrid-search.service';

// 导出设置服务
export { SettingsService, settingsService } from './settings.service';

// 导出黑名单服务
export { BlacklistService, blacklistService } from './blacklist.service';

// 导出数据导入导出服务
export { ExportService, exportService } from './export.service';
export { ImportService, importService } from './import.service';

// 导出类型（如果需要的话）
export type { IndexedDBService as IIndexedDBService } from './db.service';
export type { SearchService as ISearchService } from './search.service';
export type { SettingsService as ISettingsService } from './settings.service';
export type { BlacklistService as IBlacklistService } from './blacklist.service';
export type { ExportService as IExportService } from './export.service';
export type { ImportService as IImportService } from './import.service';

// 导出工具函数
export { 
  debounce, 
  createConfigInputDebounce, 
  logger,
  dbLogger,
  searchLogger,
  contentLogger,
  backgroundLogger,
  AppError,
  ErrorCode,
  errorManager,
  safeAsync,
  safeSync,
  withErrorHandling,
  LogLevel,
  type DebouncedFunction,
  type LoggerConfig,
  type ErrorInfo,
  type ErrorHandler
} from '../utils';

// 导出通知管理器
export { NotificationManager, NotificationType } from '../notifications';
