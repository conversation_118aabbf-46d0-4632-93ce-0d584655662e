/**
 * Intelligent DOM Parser for Recall V3.0
 * 
 * Advanced DOM analysis and content extraction that goes beyond basic text extraction.
 * Handles complex page layouts, modern frameworks, and semantic HTML structures.
 */

/**
 * DOM parsing result interface
 */
export interface DOMParsingResult {
  /** Whether parsing was successful */
  success: boolean;
  /** Extracted text content */
  content: string;
  /** Error message if any */
  error?: string;
  /** Confidence score (0-1) of extraction quality */
  confidence: number;
  /** Semantic elements found */
  semanticElements: SemanticElement[];
}

/**
 * Semantic element interface
 */
export interface SemanticElement {
  /** Element type */
  type: ElementType;
  /** Text content */
  content: string;
  /** Element importance score */
  importance: number;
  /** Element selector for reference */
  selector: string;
}

/**
 * Element type constants
 */
export const ElementType = {
  HEADING: 'heading',
  PARAGRAPH: 'paragraph',
  LIST: 'list',
  CODE: 'code',
  QUOTE: 'quote',
  TABLE: 'table',
  CAPTION: 'caption',
  FIGURE: 'figure',
  ASIDE: 'aside',
  NAVIGATION: 'navigation'
} as const;

export type ElementType = typeof ElementType[keyof typeof ElementType];

/**
 * Content scoring weights for different elements
 */
const ELEMENT_WEIGHTS = {
  'article': 1.0,
  'main': 0.9,
  '[role="main"]': 0.9,
  'section': 0.8,
  '.content': 0.8,
  '.post': 0.8,
  '.entry': 0.8,
  '.article': 0.8,
  '.story': 0.8,
  '.text': 0.7,
  '.body': 0.7,
  '.markdown-body': 0.9,
  'h1': 0.9,
  'h2': 0.8,
  'h3': 0.7,
  'h4': 0.6,
  'h5': 0.5,
  'h6': 0.4,
  'p': 0.6,
  'blockquote': 0.7,
  'pre': 0.8,
  'code': 0.8,
  'ul': 0.6,
  'ol': 0.6,
  'li': 0.5,
  'table': 0.7,
  'figure': 0.6,
  'figcaption': 0.5
};

/**
 * Elements to remove during parsing
 */
const NOISE_SELECTORS = [
  'script',
  'style',
  'nav',
  'header',
  'footer',
  'aside:not(.content)',
  '.navigation',
  '.nav',
  '.menu',
  '.sidebar',
  '.widget',
  '.ads',
  '.advertisement',
  '.promotion',
  '.banner',
  '.popup',
  '.modal',
  '.overlay',
  '.cookie-notice',
  '.gdpr-notice',
  '.newsletter',
  '.subscription',
  '.social-share',
  '.related',
  '.recommended',
  '.comments',
  '.comment',
  '.pagination',
  '.breadcrumb',
  '.tag',
  '.tags',
  '.category',
  '.categories',
  '.metadata',
  '.byline:not(.author)',
  '.date-posted',
  '.share-buttons',
  '[role="navigation"]',
  '[role="banner"]',
  '[role="contentinfo"]',
  '[role="complementary"]',
  '[aria-hidden="true"]',
  '.sr-only',
  '.visually-hidden',
  '.screen-reader-text'
];

/**
 * Advanced DOM Parser class
 */
export class DOMParser {
  
  /**
   * Extract content using intelligent DOM parsing
   */
  public extractContent(): DOMParsingResult {
    try {
      // Clone document to avoid modifying original
      const documentClone = document.cloneNode(true) as Document;
      const body = documentClone.body;

      if (!body) {
        return {
          success: false,
          content: '',
          error: 'Document body not found',
          confidence: 0,
          semanticElements: []
        };
      }

      // Remove noise elements
      this.removeNoiseElements(body);

      // Find and score content candidates
      const candidates = this.findContentCandidates(body);

      // Select best content area
      const bestCandidate = this.selectBestCandidate(candidates);

      if (!bestCandidate) {
        return {
          success: false,
          content: '',
          error: 'No suitable content candidate found',
          confidence: 0,
          semanticElements: []
        };
      }

      // Extract semantic elements
      const semanticElements = this.extractSemanticElements(bestCandidate.element);

      // Extract final text content
      const content = this.extractTextContent(bestCandidate.element);

      // Calculate confidence score
      const confidence = this.calculateConfidence(bestCandidate, semanticElements, content);

      return {
        success: true,
        content: content.trim(),
        confidence,
        semanticElements
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `DOM parsing failed: ${(error as Error).message}`,
        confidence: 0,
        semanticElements: []
      };
    }
  }

  /**
   * Remove noise elements from the DOM
   */
  private removeNoiseElements(container: HTMLElement): void {
    NOISE_SELECTORS.forEach(selector => {
      const elements = container.querySelectorAll(selector);
      elements.forEach(element => {
        // Additional checks to avoid removing important content
        if (!this.isImportantContent(element)) {
          element.remove();
        }
      });
    });
  }

  /**
   * Check if an element contains important content despite matching noise selectors
   */
  private isImportantContent(element: Element): boolean {
    const text = element.textContent || '';
    const wordCount = text.trim().split(/\s+/).length;
    
    // Keep elements with substantial text content
    if (wordCount > 50) {
      return true;
    }

    // Keep elements with important semantic meaning
    const importantRoles = ['main', 'article', 'section'];
    const role = element.getAttribute('role');
    if (role && importantRoles.includes(role)) {
      return true;
    }

    // Keep elements with important classes/IDs that suggest content
    const classList = element.className.toLowerCase();
    const id = element.id.toLowerCase();
    const contentIndicators = ['content', 'article', 'post', 'story', 'text', 'body', 'main'];
    
    if (contentIndicators.some(indicator => 
      classList.includes(indicator) || id.includes(indicator))) {
      return true;
    }

    return false;
  }

  /**
   * Find potential content candidates in the DOM
   */
  private findContentCandidates(container: HTMLElement): Array<{ element: HTMLElement; score: number }> {
    const candidates: Array<{ element: HTMLElement; score: number }> = [];

    // Score all potential content elements
    const allElements = container.querySelectorAll('*');
    
    allElements.forEach(element => {
      if (element instanceof HTMLElement) {
        const score = this.scoreElement(element);
        if (score > 0) {
          candidates.push({ element, score });
        }
      }
    });

    // Sort by score (highest first)
    candidates.sort((a, b) => b.score - a.score);

    return candidates;
  }

  /**
   * Score an element based on its semantic value and content
   */
  private scoreElement(element: HTMLElement): number {
    let score = 0;

    // Base score from element type and selectors
    const tagName = element.tagName.toLowerCase();
    const className = element.className.toLowerCase();
    const id = element.id.toLowerCase();

    // Check against weighted selectors
    Object.entries(ELEMENT_WEIGHTS).forEach(([selector, weight]) => {
      if (element.matches(selector)) {
        score += weight;
      }
    });

    // Content length bonus
    const textContent = element.textContent || '';
    const textLength = textContent.trim().length;
    
    if (textLength > 100) {
      score += Math.min(textLength / 1000, 2); // Max bonus of 2 points
    }

    // Semantic HTML bonus
    const semanticTags = ['article', 'main', 'section', 'header', 'aside', 'figure'];
    if (semanticTags.includes(tagName)) {
      score += 0.5;
    }

    // Class/ID content indicators
    const contentIndicators = ['content', 'article', 'post', 'story', 'text', 'body', 'main', 'entry'];
    if (contentIndicators.some(indicator => 
      className.includes(indicator) || id.includes(indicator))) {
      score += 0.3;
    }

    // Penalty for navigation/UI elements
    const uiIndicators = ['nav', 'menu', 'sidebar', 'widget', 'ad', 'banner', 'header', 'footer'];
    if (uiIndicators.some(indicator => 
      className.includes(indicator) || id.includes(indicator) || tagName === indicator)) {
      score -= 0.5;
    }

    // Penalty for hidden elements
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
      score -= 1;
    }

    // Child element density (prefer elements with good text-to-child ratio)
    const childElements = element.children.length;
    const textToChildRatio = textLength / Math.max(childElements, 1);
    
    if (textToChildRatio > 50) {
      score += 0.2;
    }

    return Math.max(0, score);
  }

  /**
   * Select the best content candidate
   */
  private selectBestCandidate(candidates: Array<{ element: HTMLElement; score: number }>): { element: HTMLElement; score: number } | null {
    if (candidates.length === 0) {
      return null;
    }

    // Return the highest scoring candidate
    // Additional logic could be added here to prefer certain types of content
    return candidates[0];
  }

  /**
   * Extract semantic elements from the content area
   */
  private extractSemanticElements(container: HTMLElement): SemanticElement[] {
    const semanticElements: SemanticElement[] = [];

    // Extract headings
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      const importance = 1 - (level - 1) * 0.1; // h1=1.0, h2=0.9, etc.
      
      semanticElements.push({
        type: ElementType.HEADING,
        content: heading.textContent || '',
        importance,
        selector: this.generateSelector(heading, index)
      });
    });

    // Extract paragraphs
    const paragraphs = container.querySelectorAll('p');
    paragraphs.forEach((p, index) => {
      const text = p.textContent || '';
      if (text.trim().length > 20) { // Only meaningful paragraphs
        semanticElements.push({
          type: ElementType.PARAGRAPH,
          content: text,
          importance: 0.6,
          selector: this.generateSelector(p, index)
        });
      }
    });

    // Extract lists
    const lists = container.querySelectorAll('ul, ol');
    lists.forEach((list, index) => {
      const text = list.textContent || '';
      if (text.trim().length > 10) {
        semanticElements.push({
          type: ElementType.LIST,
          content: text,
          importance: 0.6,
          selector: this.generateSelector(list, index)
        });
      }
    });

    // Extract code blocks
    const codeBlocks = container.querySelectorAll('pre, code');
    codeBlocks.forEach((code, index) => {
      const text = code.textContent || '';
      if (text.trim().length > 5) {
        semanticElements.push({
          type: ElementType.CODE,
          content: text,
          importance: 0.8,
          selector: this.generateSelector(code, index)
        });
      }
    });

    // Extract blockquotes
    const quotes = container.querySelectorAll('blockquote');
    quotes.forEach((quote, index) => {
      const text = quote.textContent || '';
      if (text.trim().length > 10) {
        semanticElements.push({
          type: ElementType.QUOTE,
          content: text,
          importance: 0.7,
          selector: this.generateSelector(quote, index)
        });
      }
    });

    // Extract tables
    const tables = container.querySelectorAll('table');
    tables.forEach((table, index) => {
      const text = table.textContent || '';
      if (text.trim().length > 20) {
        semanticElements.push({
          type: ElementType.TABLE,
          content: text,
          importance: 0.7,
          selector: this.generateSelector(table, index)
        });
      }
    });

    // Extract figures and captions
    const figures = container.querySelectorAll('figure');
    figures.forEach((figure, index) => {
      const caption = figure.querySelector('figcaption');
      const text = caption ? caption.textContent || '' : figure.textContent || '';
      if (text.trim().length > 5) {
        semanticElements.push({
          type: ElementType.FIGURE,
          content: text,
          importance: 0.6,
          selector: this.generateSelector(figure, index)
        });
      }
    });

    return semanticElements;
  }

  /**
   * Generate a unique selector for an element
   */
  private generateSelector(element: Element, index: number): string {
    const tagName = element.tagName.toLowerCase();
    const id = element.id;
    const className = element.className;

    if (id) {
      return `#${id}`;
    }

    if (className && typeof className === 'string') {
      const classes = className.trim().split(/\s+/).slice(0, 2); // Use first 2 classes
      if (classes.length > 0) {
        return `${tagName}.${classes.join('.')}`;
      }
    }

    return `${tagName}:nth-of-type(${index + 1})`;
  }

  /**
   * Extract clean text content from an element
   */
  private extractTextContent(element: HTMLElement): string {
    // Clone to avoid modifying original
    const clone = element.cloneNode(true) as HTMLElement;

    // Remove remaining unwanted elements
    const unwantedSelectors = [
      'script', 'style', 'noscript', 'iframe', 'object', 'embed',
      '.line-numbers', '.highlight-lines', '.code-line-numbers'
    ];

    unwantedSelectors.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    // Get text content and clean it
    let text = clone.textContent || '';

    // Normalize whitespace
    text = text.replace(/\s+/g, ' ').trim();

    // Remove excessive line breaks
    text = text.replace(/\n\s*\n\s*\n/g, '\n\n');

    return text;
  }

  /**
   * Calculate confidence score for the extraction
   */
  private calculateConfidence(
    candidate: { element: HTMLElement; score: number },
    semanticElements: SemanticElement[],
    content: string
  ): number {
    let confidence = 0;

    // Base confidence from candidate score (normalized)
    confidence += Math.min(candidate.score / 3, 0.4); // Max 0.4 from score

    // Semantic structure bonus
    const hasHeadings = semanticElements.some(el => el.type === ElementType.HEADING);
    const hasParagraphs = semanticElements.some(el => el.type === ElementType.PARAGRAPH);
    const hasStructure = hasHeadings && hasParagraphs;
    
    if (hasStructure) {
      confidence += 0.2;
    }

    // Content length bonus
    const wordCount = content.split(/\s+/).length;
    if (wordCount > 100) {
      confidence += Math.min(wordCount / 1000, 0.2); // Max 0.2 bonus
    }

    // Semantic diversity bonus
    const elementTypes = new Set(semanticElements.map(el => el.type));
    confidence += elementTypes.size * 0.02; // 0.02 per unique element type

    // Content quality indicators
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);
    if (sentences.length > 5) {
      confidence += 0.1;
    }

    // Cap confidence at 1.0
    return Math.min(confidence, 1.0);
  }
}