/**
 * Generic Forum Pattern Detector
 * 
 * Automatically identifies common forum patterns and structures
 * without needing platform-specific configurations.
 * 
 * Features:
 * - Comment container detection
 * - Pagination pattern recognition
 * - Infinite scroll detection
 * - Thread/post structure analysis
 * - Generic content extraction rules
 */

export interface ForumPattern {
  type: 'comments' | 'pagination' | 'infinite-scroll' | 'thread' | 'post' | 'reply';
  element: Element;
  confidence: number;
  selectors: string[];
  metadata?: {
    itemCount?: number;
    hasTimestamps?: boolean;
    hasUsernames?: boolean;
    hasVoteButtons?: boolean;
    hasReplyButtons?: boolean;
    depth?: number; // Nesting level for replies
  };
}

export interface ForumStructure {
  mainContent: Element | null;
  commentSections: ForumPattern[];
  paginationElements: ForumPattern[];
  infiniteScrollContainers: ForumPattern[];
  threadStructure: ForumPattern[];
  confidence: number;
}

export class GenericForumDetector {
  private static readonly COMMENT_INDICATORS = [
    // Class name patterns
    /comment/i,
    /reply/i,
    /discussion/i,
    /message/i,
    /post(?!.*nav|.*header|.*footer)/i,
    /thread/i,
    /conversation/i,
    /feedback/i,
    /review/i,
    
    // ID patterns
    /comment/i,
    /reply/i,
    /discussion/i,
    
    // Attribute patterns
    /comment/i,
    /reply/i
  ];

  private static readonly PAGINATION_INDICATORS = [
    // Class name patterns
    /pag/i,
    /nav/i,
    /page/i,
    /next/i,
    /prev/i,
    /more/i,
    /load.*more/i,
    /show.*more/i,
    
    // Text content patterns
    /next/i,
    /previous/i,
    /more/i,
    /load/i,
    /page/i,
    /\d+/,
    /»/,
    /«/,
    /→/,
    /←/
  ];

  private static readonly INFINITE_SCROLL_INDICATORS = [
    /infinite/i,
    /scroll/i,
    /lazy.*load/i,
    /load.*more/i,
    /auto.*load/i,
    /stream/i,
    /feed/i,
    /endless/i
  ];

  private static readonly USERNAME_PATTERNS = [
    /user/i,
    /author/i,
    /by/i,
    /posted.*by/i,
    /username/i,
    /nickname/i,
    /profile/i,
    /avatar/i
  ];

  private static readonly TIMESTAMP_PATTERNS = [
    /time/i,
    /date/i,
    /ago/i,
    /posted/i,
    /created/i,
    /published/i,
    /\d+.*ago/i,
    /\d{4}-\d{2}-\d{2}/,
    /\d{1,2}\/\d{1,2}\/\d{4}/,
    /\d{1,2}:\d{2}/
  ];

  private static readonly VOTE_PATTERNS = [
    /vote/i,
    /like/i,
    /dislike/i,
    /up/i,
    /down/i,
    /thumb/i,
    /score/i,
    /points/i,
    /rating/i,
    /⬆/,
    /⬇/,
    /👍/,
    /👎/
  ];

  /**
   * Analyze page for forum patterns
   */
  public static analyzeForumStructure(): ForumStructure {
    const structure: ForumStructure = {
      mainContent: this.detectMainContent(),
      commentSections: this.detectCommentSections(),
      paginationElements: this.detectPagination(),
      infiniteScrollContainers: this.detectInfiniteScroll(),
      threadStructure: this.detectThreadStructure(),
      confidence: 0
    };

    // Calculate overall confidence
    structure.confidence = this.calculateStructureConfidence(structure);

    return structure;
  }

  /**
   * Detect main content area
   */
  private static detectMainContent(): Element | null {
    const candidates = [
      document.querySelector('main'),
      document.querySelector('[role="main"]'),
      document.querySelector('#main'),
      document.querySelector('.main'),
      document.querySelector('article'),
      document.querySelector('.content'),
      document.querySelector('#content'),
      document.querySelector('.post-content'),
      document.querySelector('.entry-content')
    ].filter(Boolean);

    if (candidates.length === 0) {
      // Fallback: find largest content container
      const allDivs = Array.from(document.querySelectorAll('div'));
      return allDivs.reduce((largest, current) => {
        const currentSize = current.textContent?.length || 0;
        const largestSize = largest?.textContent?.length || 0;
        return currentSize > largestSize ? current : largest;
      }, null as Element | null);
    }

    return candidates[0] as Element;
  }

  /**
   * Detect comment sections
   */
  private static detectCommentSections(): ForumPattern[] {
    const patterns: ForumPattern[] = [];
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
      const confidence = this.calculateCommentConfidence(element);
      if (confidence > 0.5) {
        const pattern = this.analyzeCommentPattern(element, confidence);
        if (pattern) {
          patterns.push(pattern);
        }
      }
    });

    // Remove nested patterns (keep parent containers)
    return this.filterNestedPatterns(patterns);
  }

  /**
   * Calculate comment section confidence
   */
  private static calculateCommentConfidence(element: Element): number {
    let confidence = 0;

    // Check class names and IDs
    const classAndId = (element.className + ' ' + element.id).toLowerCase();
    this.COMMENT_INDICATORS.forEach(pattern => {
      if (pattern.test(classAndId)) {
        confidence += 0.3;
      }
    });

    // Check for multiple child elements (suggesting a list of comments)
    const children = Array.from(element.children);
    if (children.length >= 3) {
      confidence += 0.2;
    }

    // Check for username/author patterns
    const hasUsernames = this.hasPattern(element, this.USERNAME_PATTERNS);
    if (hasUsernames) confidence += 0.2;

    // Check for timestamp patterns
    const hasTimestamps = this.hasPattern(element, this.TIMESTAMP_PATTERNS);
    if (hasTimestamps) confidence += 0.2;

    // Check for vote/like patterns
    const hasVotes = this.hasPattern(element, this.VOTE_PATTERNS);
    if (hasVotes) confidence += 0.1;

    // Check for reply buttons/links
    const hasReplyButtons = this.hasReplyButtons(element);
    if (hasReplyButtons) confidence += 0.15;

    // Check for nested structure (threaded comments)
    const hasNesting = this.hasNestedStructure(element);
    if (hasNesting) confidence += 0.1;

    // Penalize if too few text content
    const textLength = element.textContent?.trim().length || 0;
    if (textLength < 50) {
      confidence *= 0.5;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Analyze comment pattern details
   */
  private static analyzeCommentPattern(element: Element, confidence: number): ForumPattern | null {
    const children = Array.from(element.children);
    const hasUsernames = this.hasPattern(element, this.USERNAME_PATTERNS);
    const hasTimestamps = this.hasPattern(element, this.TIMESTAMP_PATTERNS);
    const hasVotes = this.hasPattern(element, this.VOTE_PATTERNS);
    const hasReplyButtons = this.hasReplyButtons(element);
    const depth = this.calculateNestingDepth(element);

    return {
      type: 'comments',
      element,
      confidence,
      selectors: this.generateSelectors(element),
      metadata: {
        itemCount: children.length,
        hasUsernames,
        hasTimestamps,
        hasVoteButtons: hasVotes,
        hasReplyButtons,
        depth
      }
    };
  }

  /**
   * Detect pagination elements
   */
  private static detectPagination(): ForumPattern[] {
    const patterns: ForumPattern[] = [];
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
      const confidence = this.calculatePaginationConfidence(element);
      if (confidence > 0.6) {
        patterns.push({
          type: 'pagination',
          element,
          confidence,
          selectors: this.generateSelectors(element)
        });
      }
    });

    return this.filterNestedPatterns(patterns);
  }

  /**
   * Calculate pagination confidence
   */
  private static calculatePaginationConfidence(element: Element): number {
    let confidence = 0;

    // Check class names and IDs
    const classAndId = (element.className + ' ' + element.id).toLowerCase();
    this.PAGINATION_INDICATORS.forEach(pattern => {
      if (pattern.test(classAndId)) {
        confidence += 0.4;
      }
    });

    // Check for navigation-like structure
    if (element.tagName.toLowerCase() === 'nav') {
      confidence += 0.3;
    }

    // Check for page numbers or navigation text
    const textContent = element.textContent?.toLowerCase() || '';
    const hasPageNumbers = /\d+/.test(textContent);
    const hasNavText = /next|previous|more|page/.test(textContent);
    
    if (hasPageNumbers) confidence += 0.2;
    if (hasNavText) confidence += 0.2;

    // Check for multiple links (typical in pagination)
    const links = element.querySelectorAll('a, button');
    if (links.length >= 2) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Detect infinite scroll containers
   */
  private static detectInfiniteScroll(): ForumPattern[] {
    const patterns: ForumPattern[] = [];
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
      const confidence = this.calculateInfiniteScrollConfidence(element);
      if (confidence > 0.5) {
        patterns.push({
          type: 'infinite-scroll',
          element,
          confidence,
          selectors: this.generateSelectors(element)
        });
      }
    });

    return patterns;
  }

  /**
   * Calculate infinite scroll confidence
   */
  private static calculateInfiniteScrollConfidence(element: Element): number {
    let confidence = 0;

    // Check class names and IDs
    const classAndId = (element.className + ' ' + element.id).toLowerCase();
    this.INFINITE_SCROLL_INDICATORS.forEach(pattern => {
      if (pattern.test(classAndId)) {
        confidence += 0.4;
      }
    });

    // Check for data attributes that suggest infinite scroll
    const hasInfiniteScrollAttrs = Array.from(element.attributes).some(attr => 
      /infinite|scroll|lazy|auto.*load/i.test(attr.name + attr.value)
    );
    if (hasInfiniteScrollAttrs) confidence += 0.3;

    // Check if element has many repeated child structures
    const children = Array.from(element.children);
    if (children.length > 10) {
      const firstChildClass = children[0]?.className;
      const similarChildren = children.filter(child => child.className === firstChildClass);
      if (similarChildren.length / children.length > 0.7) {
        confidence += 0.3;
      }
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Detect thread structure
   */
  private static detectThreadStructure(): ForumPattern[] {
    const patterns: ForumPattern[] = [];
    
    // Look for elements that contain both main content and comments
    const candidates = document.querySelectorAll('article, .thread, .post, .topic, [class*="thread"], [class*="topic"]');
    
    candidates.forEach(element => {
      const confidence = this.calculateThreadConfidence(element);
      if (confidence > 0.4) {
        patterns.push({
          type: 'thread',
          element,
          confidence,
          selectors: this.generateSelectors(element)
        });
      }
    });

    return patterns;
  }

  /**
   * Calculate thread structure confidence
   */
  private static calculateThreadConfidence(element: Element): number {
    let confidence = 0;

    // Check if contains both content and comments
    const hasContent = element.textContent && element.textContent.length > 100;
    const hasComments = this.containsCommentLikeElements(element);

    if (hasContent && hasComments) {
      confidence += 0.6;
    } else if (hasContent || hasComments) {
      confidence += 0.3;
    }

    // Check for thread-like class names
    const classAndId = (element.className + ' ' + element.id).toLowerCase();
    if (/thread|topic|post|article/i.test(classAndId)) {
      confidence += 0.2;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Helper method to check if element contains pattern
   */
  private static hasPattern(element: Element, patterns: RegExp[]): boolean {
    const text = element.textContent?.toLowerCase() || '';
    const html = element.innerHTML.toLowerCase();
    
    return patterns.some(pattern => 
      pattern.test(text) || pattern.test(html) || 
      pattern.test(element.className.toLowerCase()) ||
      pattern.test(element.id.toLowerCase())
    );
  }

  /**
   * Check for reply buttons or links
   */
  private static hasReplyButtons(element: Element): boolean {
    const buttons = element.querySelectorAll('button, a, [role="button"]');
    return Array.from(buttons).some(btn => 
      /reply|respond|answer/i.test(btn.textContent || '') ||
      /reply|respond|answer/i.test(btn.className) ||
      /reply|respond|answer/i.test(btn.id)
    );
  }

  /**
   * Check for nested comment structure
   */
  private static hasNestedStructure(element: Element): boolean {
    const depth = this.calculateNestingDepth(element);
    return depth > 2;
  }

  /**
   * Calculate nesting depth of similar elements
   */
  private static calculateNestingDepth(element: Element): number {
    let maxDepth = 0;
    
    const traverse = (el: Element, depth: number) => {
      maxDepth = Math.max(maxDepth, depth);
      
      Array.from(el.children).forEach(child => {
        if (this.isSimilarElement(child, element)) {
          traverse(child, depth + 1);
        } else {
          traverse(child, depth);
        }
      });
    };
    
    traverse(element, 0);
    return maxDepth;
  }

  /**
   * Check if two elements are structurally similar
   */
  private static isSimilarElement(el1: Element, el2: Element): boolean {
    return el1.tagName === el2.tagName &&
           el1.className === el2.className;
  }

  /**
   * Check if element contains comment-like sub-elements
   */
  private static containsCommentLikeElements(element: Element): boolean {
    const descendants = element.querySelectorAll('*');
    return Array.from(descendants).some(desc => 
      this.calculateCommentConfidence(desc) > 0.3
    );
  }

  /**
   * Generate CSS selectors for an element
   */
  private static generateSelectors(element: Element): string[] {
    const selectors: string[] = [];
    
    // ID selector
    if (element.id) {
      selectors.push(`#${element.id}`);
    }
    
    // Class selector
    if (element.className) {
      const classes = element.className.split(' ').filter(Boolean);
      if (classes.length > 0) {
        selectors.push(`.${classes.join('.')}`);
        // Also add individual class selectors
        classes.forEach(cls => selectors.push(`.${cls}`));
      }
    }
    
    // Tag selector
    selectors.push(element.tagName.toLowerCase());
    
    // Attribute selectors for data attributes
    Array.from(element.attributes).forEach(attr => {
      if (attr.name.startsWith('data-')) {
        selectors.push(`[${attr.name}="${attr.value}"]`);
      }
    });
    
    return selectors;
  }

  /**
   * Filter out nested patterns, keeping only parent containers
   */
  private static filterNestedPatterns(patterns: ForumPattern[]): ForumPattern[] {
    return patterns.filter((pattern, index) => {
      // Check if this pattern is contained within any other pattern
      return !patterns.some((other, otherIndex) => 
        index !== otherIndex && 
        other.element.contains(pattern.element) &&
        other.confidence >= pattern.confidence
      );
    });
  }

  /**
   * Calculate overall structure confidence
   */
  private static calculateStructureConfidence(structure: ForumStructure): number {
    let totalConfidence = 0;
    let componentCount = 0;

    if (structure.mainContent) {
      totalConfidence += 0.3;
      componentCount++;
    }

    if (structure.commentSections.length > 0) {
      const avgCommentConfidence = structure.commentSections.reduce((sum, pattern) => 
        sum + pattern.confidence, 0) / structure.commentSections.length;
      totalConfidence += avgCommentConfidence * 0.4;
      componentCount++;
    }

    if (structure.paginationElements.length > 0) {
      totalConfidence += 0.2;
      componentCount++;
    }

    if (structure.infiniteScrollContainers.length > 0) {
      totalConfidence += 0.1;
      componentCount++;
    }

    return componentCount > 0 ? totalConfidence / componentCount : 0;
  }

  /**
   * Get best comment container
   */
  public static getBestCommentContainer(): Element | null {
    const structure = this.analyzeForumStructure();
    
    if (structure.commentSections.length === 0) {
      return null;
    }

    // Return the comment section with highest confidence
    const bestSection = structure.commentSections.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    );

    return bestSection.element;
  }

  /**
   * Check if page appears to be a forum
   */
  public static isForumPage(): boolean {
    const structure = this.analyzeForumStructure();
    return structure.confidence > 0.5;
  }

  /**
   * Get forum type estimation
   */
  public static estimateForumType(): string {
    const structure = this.analyzeForumStructure();
    
    if (structure.commentSections.length > 0) {
      const hasVoting = structure.commentSections.some(section => 
        section.metadata?.hasVoteButtons
      );
      const hasNesting = structure.commentSections.some(section => 
        (section.metadata?.depth || 0) > 2
      );
      
      if (hasVoting && hasNesting) return 'discussion-forum'; // Reddit-like
      if (hasVoting) return 'social-forum'; // Social media
      if (hasNesting) return 'traditional-forum'; // Traditional nested forums
      return 'comment-system'; // Simple comment systems
    }
    
    return 'unknown';
  }
}