/**
 * English language resources
 */

export default {
  // Common/General
  common: {
    loading: 'Loading...',
    search: 'Search',
    clear: 'Clear',
    close: 'Close',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    remove: 'Remove',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    retry: 'Retry',
    refresh: 'Refresh',
    settings: 'Settings',
    about: 'About',
    help: 'Help',
    language: 'Language',
    all: 'All',
    relevance: 'Relevance',
    syntax: 'Syntax'
  },

  // Main App
  app: {
    title: 'Recall',
    subtitle: 'Intelligent History Search',
    searchPlaceholder: 'Search your browsing history...',
    clearSearch: 'Clear search',
    toggleDensity: 'Toggle density',
    densityMode: {
      compact: 'Compact Density',
      comfortable: 'Comfortable Density'
    }
  },

  // Search
  search: {
    placeholder: 'Search...',
    noResults: 'No results found',
    searching: 'Searching...',
    results: 'results',
    result: 'result',
    searchTime: 'Search completed in {{time}}ms',
    clearSearchTitle: 'Clear search',
    syntaxHelp: 'Search syntax help'
  },

  // Error handling
  errors: {
    // Error types
    serviceInit: {
      title: 'Service Initialization Failed',
      message: 'Search service could not start properly, might be a browser compatibility issue',
      solutions: [
        'Refresh the page and try again',
        'Check if your browser supports IndexedDB',
        'Clear browser cache and data',
        'Restart your browser'
      ]
    },
    search: {
      title: 'Search Service Error',
      message: 'Search functionality is temporarily unavailable, might be a data indexing issue',
      solutions: [
        'Check if search keywords are valid',
        'Try simplifying your search terms',
        'Wait a few seconds and try again',
        'Refresh the page to start over'
      ]
    },
    network: {
      title: 'Network Connection Issue',
      message: 'Cannot connect to required services, please check your network status',
      solutions: [
        'Check if network connection is working',
        'Refresh the page and try again',
        'Try again later'
      ]
    },
    database: {
      title: 'Data Storage Issue',
      message: 'Local database access error, data cleanup might be needed',
      solutions: [
        'Check browser storage space',
        'Clear extension data and start fresh',
        'Reset data in extension settings',
        'Contact technical support'
      ]
    },
    unknown: {
      title: 'Unknown Error',
      message: 'An unexpected error occurred, please try refreshing the page',
      solutions: [
        'Refresh the page and try again',
        'Restart your browser',
        'Check if extension is running properly',
        'Contact support if problem persists'
      ]
    },
    // Error actions
    reload: '🔄 Reload',
    closeError: '✕ Close Error',
    openSettings: '⚙️ Open Settings',
    solutionsTitle: '💡 Solutions'
  },

  // Empty state
  empty: {
    title: 'Start searching your history',
    description: 'Enter keywords above to quickly find pages you\'ve visited before',
    features: {
      fuzzyMatch: 'Smart fuzzy matching',
      multiFilter: 'Multi-dimensional filtering',
      fastResponse: 'Millisecond response'
    },
    searchTips: {
      title: 'Search Tips',
      items: [
        'Supports mixed Chinese and English search',
        'Can search page titles, URLs and content',
        'Use filters for precise results'
      ]
    },
    keyboardShortcuts: {
      title: '⌨️ Keyboard Shortcuts',
      focusSearch: 'Focus search',
      clearSearch: 'Clear search',
      browseResults: 'Browse results',
      openPage: 'Open page',
      quickOpen: 'Quick open'
    },
    searchExamples: {
      title: 'Try these search examples',
      basic: {
        text: 'React JavaScript',
        description: 'Basic search'
      },
      exact: {
        text: '"best practices"',
        description: 'Exact match'
      },
      site: {
        text: 'site:github.com',
        description: 'Site-specific'
      },
      exclude: {
        text: 'Vue -tutorial',
        description: 'Exclude search'
      }
    }
  },

  // Options page
  options: {
    title: 'Configuration Center',
    pageNotFound: 'Page not found',
    selectOption: 'Please select an option from the left navigation.',
    loadingConfig: 'Loading configuration center...',
    
    // App settings
    app: {
      title: 'App Settings',
      description: 'Configure general application settings.',
      settings: {
        theme: {
          label: 'Theme',
          description: 'Choose between light and dark themes.',
          options: {
            light: 'Light',
            dark: 'Dark',
            system: 'System'
          }
        },
        density: {
          label: 'Density',
          description: 'Adjust the information density of the interface.',
          options: {
            compact: 'Compact',
            default: 'Default',
            comfortable: 'Comfortable'
          }
        },
        language: {
          label: 'Language',
          description: 'Set the display language of the application.'
        }
      }
    },

    // Common options translations
    common: {
      loading: 'Loading...',
      saving: 'Saving...',
      saved: 'Settings saved',
      saveFailed: 'Save failed, please retry',
      resetToDefaults: 'Reset to defaults',
      resetConfirm: 'Are you sure you want to reset to default settings?',
      configure: 'Configure',
      configured: 'Configured',
      unconfigured: 'Not configured',
      testConnection: 'Test connection',
      removeKey: 'Remove key',
      getKey: 'Get key',
      saveKey: 'Save key',
      cancel: 'Cancel',
      close: '×',
      enabled: 'Enabled',
      disabled: 'Disabled',
      version: 'Version {{version}}',
      units: {
        items: 'items',
        ms: 'ms',
        seconds: 'seconds',
        minutes: 'minutes',
        mb: 'MB',
        percent: '%'
      }
    },

    // API Key Management
    apiKeys: {
      title: '🔑 API Key Management',
      description: 'Configure your AI service provider API keys and enable BYOK (Bring Your Own Key) mode',
      loading: 'Loading API key configuration...',
      privacy: {
        title: 'Privacy Protection',
        points: [
          'All API keys are encrypted with AES-256 and stored locally',
          'Keys connect directly to the corresponding AI service providers, not through our servers',
          'You have complete control over your data and key security'
        ]
      },
      providers: {
        openai: {
          name: 'OpenAI',
          description: 'GPT-4, GPT-3.5 and other models',
          setupGuide: 'Create API key on OpenAI platform',
          keyFormat: 'sk-...'
        },
        anthropic: {
          name: 'Anthropic',
          description: 'Claude series models',
          setupGuide: 'Create API key in Anthropic Console',
          keyFormat: 'sk-ant-...'
        },
        deepseek: {
          name: 'DeepSeek',
          description: 'DeepSeek Chat models',
          setupGuide: 'Create API key on DeepSeek platform',
          keyFormat: 'sk-...'
        },
        google: {
          name: 'Google AI',
          description: 'Gemini series models',
          setupGuide: 'Create API key in Google AI Studio',
          keyFormat: 'AI...'
        },
        azure: {
          name: 'Azure OpenAI',
          description: 'Azure-hosted OpenAI models',
          setupGuide: 'Configure OpenAI resource in Azure Portal',
          keyFormat: 'Requires endpoint and key'
        },
        litellm: {
          name: 'LiteLLM',
          description: 'Unified interface supporting multiple model providers',
          setupGuide: 'Configure LiteLLM proxy endpoint',
          keyFormat: 'Depends on specific provider'
        }
      },
      modal: {
        title: 'Configure {{providerName}} API Key',
        apiKeyLabel: 'API Key',
        apiKeyPlaceholder: 'Enter your {{providerName}} API key',
        endpointLabel: 'API Endpoint',
        endpointPlaceholder: 'https://your-resource.openai.azure.com/',
        securityNotice: '🔐 Keys will be securely stored locally using AES-256 encryption algorithm, only for extension use'
      },
      messages: {
        invalidKey: 'Please enter a valid API key',
        saveSuccess: 'API key saved securely',
        saveError: 'Save failed, please retry',
        removeConfirm: 'Are you sure you want to delete the API key for {{providerName}}?',
        removeSuccess: 'API key deleted',
        removeError: 'Delete failed, please retry',
        testSuccess: 'API key verification successful',
        testError: 'API key verification failed',
        keyNotFound: 'API key not found'
      }
    },

    // Search Settings
    search: {
      title: '⚙️ Search Settings',
      description: 'Configure traditional search and keyword search behavior parameters',
      loading: 'Loading search settings...',
      sections: {
        results: {
          title: '📊 Search Results',
          description: 'Control search result quantity and display'
        },
        traditional: {
          title: '🔍 Traditional Search',
          description: 'Fuzzy keyword matching and content search settings'
        },
        keyword: {
          title: '🔍 Keyword Search',
          description: 'Traditional keyword matching search settings'
        },
        indexing: {
          title: '📚 Content Indexing',
          description: 'Automatic page content indexing settings'
        },
        userExperience: {
          title: '🎯 User Experience',
          description: 'Search interface and interaction experience settings'
        }
      },
      settings: {
        maxResults: {
          label: 'Maximum search results',
          description: 'Maximum number of results returned in a single search'
        },
        searchTimeout: {
          label: 'Search timeout',
          description: 'Maximum wait time for search requests'
        },
        enableTraditionalSearch: {
          label: 'Enable traditional search',
          description: 'Use traditional keyword matching and fuzzy search'
        },
        traditionalSearchWeight: {
          label: 'Traditional search weight',
          description: 'Traditional search weight ratio in hybrid search'
        },
        enableKeywordSearch: {
          label: 'Enable keyword search',
          description: 'Use traditional keyword matching search'
        },
        fuzzySearchThreshold: {
          label: 'Fuzzy search threshold',
          description: 'Control fuzzy matching sensitivity (smaller is stricter)'
        },
        keywordSearchWeight: {
          label: 'Keyword search weight',
          description: 'Keyword search weight ratio in hybrid search'
        },
        enableIndexing: {
          label: 'Enable auto indexing',
          description: 'Automatically index visited page content'
        },
        indexingDelay: {
          label: 'Indexing delay',
          description: 'Delay after page load before starting indexing'
        },
        enableSearchHistory: {
          label: 'Enable search history',
          description: 'Remember recent search queries'
        },
        enableAutoComplete: {
          label: 'Enable auto-complete',
          description: 'Show search suggestions while typing'
        },
        enableDebugMode: {
          label: 'Enable debug mode',
          description: 'Show detailed search debugging information'
        }
      },
      // Hybrid search configuration
      hybridSearchConfig: 'Hybrid Search Configuration',
      hybridSearchDescription: 'Configure the weight distribution of different search engines in hybrid search. Total weight must equal 100%.',
      weight: 'Weight',
      totalWeight: 'Total Weight',
      alwaysShow: 'Always Show',
      loadingConfig: 'Loading configuration...',
      configSaved: 'Configuration saved successfully',
      configSaveFailed: 'Failed to save configuration',
      resetConfigConfirm: 'Are you sure you want to reset search engine configuration to defaults?',
      engines: {
        traditional: {
          title: 'Traditional Search',
          description: 'Fuzzy keyword matching with typo tolerance'
        },
        fulltext: {
          title: 'Full-text Search',
          description: 'Exact phrase and content matching'
        }
      }
    },

    // Reading Assistant
    readingAssistant: {
      title: '📖 Reading Assistant',
      description: 'Configure intelligent reading assistant and AI summary features to enhance your reading experience',
      loading: 'Loading reading assistant settings...',
      sections: {
        detection: {
          title: '🕐 Reading Detection',
          description: 'Automatically detect your reading behavior and duration'
        },
        notifications: {
          title: '🔔 Reading Reminders',
          description: 'Show non-intrusive reminders during long reading sessions'
        },
        aiSummary: {
          title: '🤖 AI Summary',
          description: 'Use AI to generate intelligent summaries for long articles'
        },
        general: {
          title: '⚙️ General Settings',
          description: 'Other reading assistant related settings'
        }
      },
      settings: {
        enableReadingDetection: {
          label: 'Enable reading detection',
          description: 'Monitor page dwell time and active status'
        },
        minimumReadingTime: {
          label: 'Minimum reading time',
          description: 'Visits shorter than this time are not recorded as reading'
        },
        enableToastNotifications: {
          label: 'Enable reading reminders',
          description: 'Show reading duration reminders in the bottom-right corner'
        },
        readingTimeThreshold: {
          label: 'Reminder trigger time',
          description: 'Show reminder after reaching this reading duration'
        },
        enableAutoSummary: {
          label: 'Enable AI summary',
          description: 'Automatically prompt to generate summaries for long articles'
        },
        summaryTriggerThreshold: {
          label: 'Summary trigger time',
          description: 'Prompt to generate summary after reading for this duration'
        },
        summaryPosition: {
          label: 'Summary position',
          description: 'Display position of summary panel on the page',
          options: {
            topRight: 'Top right',
            topLeft: 'Top left',
            bottomRight: 'Bottom right',
            bottomLeft: 'Bottom left'
          }
        },
        summaryLanguage: {
          label: 'Summary language',
          description: 'Language preference for summary generation',
          options: {
            auto: 'Auto detect',
            zh: 'Chinese',
            en: 'English'
          }
        },
        summaryLength: {
          label: 'Summary length',
          description: 'Detailed level of generated summary',
          options: {
            short: 'Brief (1-2 sentences)',
            medium: 'Medium (1 paragraph)',
            long: 'Detailed (multiple paragraphs)'
          }
        },
        enableKeyboardShortcuts: {
          label: 'Enable keyboard shortcuts',
          description: 'Use Ctrl+Shift+S to quickly generate summary'
        }
      },
      apiNotice: {
        title: '⚠️ API Key Required',
        description: 'AI summary features require configuring LLM service provider API keys. Please go to the {{apiKeyManagement}} page to configure.',
        configureButton: 'Configure API Keys'
      }
    },

    // About
    about: {
      title: 'Recall',
      version: 'Version {{version}}',
      tagline: 'Solving the pain point of users "have seen but can\'t find", making your browsing history truly useful through intelligent full-text search.',
      features: {
        title: 'Core Features',
        items: {
          fullTextSearch: {
            title: 'Full-text Search',
            description: 'Not just titles, but complete page content.'
          },
          fastResponse: {
            title: 'Lightning Fast',
            description: 'Real-time search results with millisecond response.'
          },
          privacyFirst: {
            title: 'Privacy First',
            description: 'All data stored locally, never uploaded to cloud.'
          },
          smartMatching: {
            title: 'Smart Matching',
            description: 'Supports fuzzy search and advanced query syntax.'
          }
        }
      },
      privacy: {
        title: 'Privacy Promise',
        description: 'We firmly believe your data belongs to you. Recall securely stores all browsing history indexes locally in your browser. We never have and never will upload your data to any server. This project is completely open source, and you can review the code at any time to verify our commitment.'
      },
      links: {
        title: 'Useful Links',
        items: {
          github: '⭐ Give us a star on GitHub',
          guide: '❓ View usage guide',
          review: '📝 Review us on Chrome Store'
        }
      },
      acknowledgements: 'Thanks to {{readability}} and {{fusejs}} for their powerful support.',
      copyright: '© 2025 Recall. Made with ❤️ by penwyp.'
    },

    // Data Backup
    dataBackup: {
      title: '💾 Data Backup & Import',
      description: 'Export and import your browsing history data',
      loading: 'Loading data backup interface...',
      stats: {
        title: 'Data Statistics',
        totalPages: 'Total pages: {{count}}',
        totalBlacklist: 'Blacklisted domains: {{count}}',
        lastUpdate: 'Last updated: {{time}}'
      },
      export: {
        title: 'Export Data',
        description: 'Export all your browsing history and settings as a JSON file',
        button: 'Export All Data',
        processing: 'Exporting...',
        progress: 'Exporting... {{progress}}%',
        success: 'Data exported successfully',
        error: 'Export failed: {{error}}'
      },
      import: {
        title: 'Import Data',
        description: 'Import previously exported data file',
        button: 'Select Import File',
        processing: 'Processing import...',
        success: 'Import completed successfully',
        error: 'Import failed: {{error}}',
        invalidFormat: 'Invalid backup file format',
        stats: {
          pagesImported: 'Pages imported: {{count}}',
          blacklistImported: 'Blacklist imported: {{count}}',
          pagesSkipped: 'Pages skipped: {{count}}',
          blacklistSkipped: 'Blacklist skipped: {{count}}'
        }
      },
      warnings: {
        exportSize: 'Export file may be large depending on your history size',
        importOverwrite: 'Import will merge with existing data, duplicates will be skipped',
        backupFirst: 'Recommend backing up current data before importing'
      },
      dangerZone: {
        title: 'Danger Zone',
        description: 'Clearing all data will permanently delete all browsing history and blacklist. This operation cannot be undone. Please ensure you have backed up important data before proceeding.',
        clearAllButton: '🗑️ Clear All Data',
        clearAllConfirm: 'Are you sure you want to clear all data? This will permanently delete all browsing history and blacklist. This operation cannot be undone!',
        clearAllDoubleConfirm: 'Please confirm again: This will permanently delete all data, including browsing history and blacklist. Are you sure you want to continue?',
        clearAllSuccess: 'All data has been cleared',
        clearAllError: 'Clear failed, please retry'
      },
      importResults: {
        title: 'Import Results',
        pagesImported: 'History imported:',
        blacklistImported: 'Blacklist imported:',
        pagesSkipped: 'History skipped:',
        blacklistSkipped: 'Blacklist skipped:',
        entries: '{{count}} entries',
        importErrors: 'Import errors:',
        moreErrors: '... and {{count}} more errors'
      }
    },

    // History Management
    historyManagement: {
      title: '📚 History Management',
      description: 'View, search and manage your browsing history',
      loading: 'Loading history records...',
      stats: {
        totalPages: 'Total pages: {{count}}',
        totalDomains: 'Unique domains: {{count}}',
        dateRange: 'From {{oldest}} to {{newest}}',
        totalSize: 'Total size: {{size}} MB'
      },
      search: {
        placeholder: 'Search titles, URLs and content...',
        noResults: 'No matching history found',
        searching: 'Searching...'
      },
      sorting: {
        label: 'Sort:',
        options: {
          visitTimeDesc: 'Latest visited',
          visitTimeAsc: 'Earliest visited',
          lastUpdatedDesc: 'Recently updated',
          lastUpdatedAsc: 'Earliest updated',
          domainAsc: 'Domain (A-Z)',
          idDesc: 'ID order'
        }
      },
      actions: {
        selectAll: 'Select all',
        deselectAll: 'Deselect all',
        deleteSelected: 'Delete selected',
        deleteConfirm: 'Are you sure you want to delete "{{title}}"?',
        deleteSuccess: 'Delete successful',
        deleteError: 'Delete failed, please try again',
        exportSelected: 'Export selected',
        refreshList: 'Refresh list'
      },
      item: {
        visitTime: 'Visited {{time}}',
        readingTime: 'Read for {{duration}}',
        accessCount: 'Accessed {{count}} times',
        accessCountTooltip: 'Access count',
        lastVisitTooltip: 'Last visit time',
        contentSizeTooltip: 'Content size',
        delete: 'Delete this record',
        deleteAriaLabel: 'Delete {{title}}',
        openInNewTab: 'Open in new tab',
        sizeKB: '{{size}} KB'
      },
      emptyState: {
        noHistory: 'No history records found',
        noSearchResults: 'No pages containing "{{query}}" found',
        startBrowsing: 'Start browsing the web to build your history'
      }
    },

    // Blacklist Management
    blacklistManagement: {
      title: 'Blacklist',
      description: 'Manage website domains you don\'t want indexed',
      loading: 'Loading...',
      
      // Header stats
      stats: {
        totalDomains: 'Total domains: {{count}}',
        wildcardDomains: 'Wildcards: {{count}}'
      },
      
      // Search functionality
      search: {
        placeholder: 'Search domains and reasons...'
      },
      
      // Add domain modal and form
      addDomain: {
        modalTitle: 'Add New Blocked Domain',
        domainLabel: 'Domain',
        domainPlaceholder: 'example.com',
        domainPlaceholderWildcard: '*.example.com',
        reasonLabel: 'Reason (optional)',
        reasonPlaceholder: 'Explain reason (optional)',
        wildcardLabel: 'Wildcard match (matches all subdomains)',
        submitButton: 'Confirm Add',
        submitButtonLoading: 'Adding...',
        cancelButton: 'Cancel',
        
        // Validation errors
        errors: {
          domainRequired: 'Domain cannot be empty',
          domainInvalid: 'Please enter a valid domain format',
          wildcardInvalid: 'Wildcard domains must start with *.'
        },
        
        // Status messages
        addFailed: 'Add failed, please try again'
      },
      
      // Domain list and items
      domainList: {
        noReason: 'None',
        exactMatch: 'Exact',
        wildcardMatch: 'Wildcard',
        exactMatchTooltip: 'Exact match',
        wildcardMatchTooltip: 'Wildcard match',
        addedTimeTooltip: 'Added time: {{time}}',
        removeTooltip: 'Remove from blacklist',
        removeAriaLabel: 'Remove {{domain}}'
      },
      
      // Bulk actions
      bulkActions: {
        clearAll: 'Clear All',
        clearAllTooltip: 'Clear all blocked domains',
        addDomain: 'Add Domain',
        addDomainTooltip: 'Add new blocked domain'
      },
      
      // Confirmation dialogs
      confirmations: {
        removeDomain: 'Are you sure you want to remove "{{domain}}" from the blacklist? This operation cannot be undone.',
        clearAll: 'Are you sure you want to clear the entire blacklist ({{count}} entries)? This operation cannot be undone.',
        deleteFailed: 'Delete failed, please try again',
        clearFailed: 'Clear failed, please try again'
      },
      
      // Empty states
      emptyState: {
        noResults: 'No results found',
        noResultsDescription: 'No domains found related to "{{query}}"',
        empty: 'Blacklist is empty',
        emptyDescription: 'Add domains to the blacklist, related pages will not be indexed',
        addFirstDomain: 'Add first domain'
      },
      
      // Help tooltip
      help: {
        title: 'Usage Instructions',
        exactMatch: 'Exact match: Only matches the specified domain, like "example.com"',
        wildcardMatch: 'Wildcard match: Matches domain and all its subdomains, like "*.example.com"',
        autoEffect: 'Auto effective: Domains added to blacklist will not be indexed by new pages',
        existingData: 'Existing data: Blacklist does not affect already indexed pages, need manual deletion'
      }
    },
    
    // Language settings
    languageSettings: {
      description: 'Configure the interface language and localization preferences.',
      interfaceLanguage: 'Interface Language',
      interfaceDescription: 'Choose the language for the extension interface.',
      note: 'Language changes will take effect immediately without requiring a restart.'
    },
    
    // Navigation items
    navigation: {
      historyManagement: {
        label: 'History Records',
        description: 'View, search and manage your browsing history'
      },
      blacklistManagement: {
        label: 'Blacklist',
        description: 'Manage website domains you don\'t want indexed'
      },
      dataBackup: {
        label: 'Import & Export',
        description: 'Export and import your history data'
      },
      searchSettings: {
        label: 'Search Settings',
        description: 'Configure search behavior and preferences'
      },
      apiKeyManagement: {
        label: 'API Key Management',
        description: 'Manage API keys for AI service providers'
      },
      readingAssistant: {
        label: 'Reading Assistant',
        description: 'Configure smart reading assistant and AI summary features'
      },
      languageSettings: {
        label: 'Language',
        description: 'Change interface language and localization settings'
      },
      about: {
        label: 'About',
        description: 'Version information and usage help'
      }
    }
  },

  // Search Bar
  searchBar: {
    suggestions: 'Search suggestions',
    noSuggestions: 'No suggestions'
  },

  // Status Bar
  statusBar: {
    systemStatus: 'System Status',
    normalOperation: 'Running Normally',
    needsAttention: 'Needs Attention',
    justNow: 'Just now',
    minutesAgo: '{{count}} minutes ago',
    hoursAgo: '{{count}} hours ago',
    daysAgo: '{{count}} days ago',
    unknown: 'Unknown',
    veryFast: 'Very Fast',
    fast: 'Fast',
    normal: 'Normal',
    slow: 'Slow',
    systemDetails: 'System Details',
    lastUpdate: 'Last Update',
    storageInfo: 'Storage Info',
    performanceMetrics: 'Performance Metrics',
    dataManagement: 'Data Management',
    backupData: 'Backup Data',
    pages: 'Pages',
    showDetails: 'Show Details',
    hideDetails: 'Hide Details',
    refreshStats: 'Refresh Statistics',
    openSettings: 'Open Settings',
    indexSize: 'Index Size',
    totalPages: 'Total Pages',
    searchSpeed: 'Search Speed',
    responseTime: 'Response Time'
  },

  // Filters
  filters: {
    all: 'All',
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    relevance: 'Relevance',
    time: 'Time',
    accessCount: 'Access Count'
  },

  // Syntax Help
  syntaxHelp: {
    title: 'Advanced Search Syntax',
    close: 'Close help',
    intro: 'Use the following syntax to precisely search your browsing history:',
    examples: {
      basic: 'Search for pages containing React and JavaScript',
      exact: 'Exact phrase matching (must match completely)',
      exclude: 'Exclude results containing specific words',
      site: 'Search only pages from specific websites',
      complex: 'Compound query: contains React, exact match "component lifecycle", excludes class, limited to reactjs.org'
    },
    tips: {
      title: 'Usage Tips',
      items: {
        '0': 'Can combine multiple syntaxes',
        '1': 'Content in quotes will be matched exactly',
        '2': 'The minus sign (-) must be attached to the word to exclude',
        '3': 'site: supports subdomain matching',
        '4': 'Search is case-insensitive'
      }
    },
    startSearch: 'Start Search',
    viewHelp: 'View search syntax help',
    syntax: 'Syntax'
  },

  // AI System
  ai: {
    initialization: {
      title: 'Initializing AI Engine',
      message: 'Initializing search engine for the first time...',
      progress: 'This may take a moment'
    }
  },

  // Manifest localization
  manifest: {
    name: 'Recall',
    description: 'AI-powered browser history search and knowledge management extension',
    actionTitle: 'Recall - Smart History Search'
  }
} as const;