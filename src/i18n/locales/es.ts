/**
 * Spanish language resources
 */

export default {
  // Common/General
  common: {
    loading: 'Cargando...',
    search: 'Buscar',
    clear: 'Limpiar',
    close: 'Cerrar',
    save: 'Guardar',
    cancel: 'Cancelar',
    delete: 'Eliminar',
    edit: 'Editar',
    add: 'Agregar',
    remove: 'Quitar',
    yes: 'Sí',
    no: 'No',
    ok: 'OK',
    retry: 'Reintentar',
    refresh: 'Actualizar',
    settings: 'Configuración',
    about: 'Acerca de',
    help: 'Ayuda',
    language: 'Idioma',
    all: 'Todo',
    relevance: 'Relevancia',
    syntax: 'Sintaxis'
  },

  // Main App
  app: {
    title: 'Recall',
    subtitle: 'Búsqueda Inteligente de Historial',
    searchPlaceholder: 'Buscar en tu historial de navegación...',
    clearSearch: 'Limpiar búsqueda',
    toggleDensity: 'Alternar densidad',
    densityMode: {
      compact: 'Densidad compacta',
      comfortable: 'Densidad cómoda'
    }
  },

  // Search
  search: {
    placeholder: 'Buscar...',
    noResults: 'No se encontraron resultados',
    searching: 'Buscando...',
    results: 'resultados',
    result: 'resultado',
    searchTime: 'Búsqueda completada en {{time}}ms',
    clearSearchTitle: 'Limpiar búsqueda',
    syntaxHelp: 'Ayuda de sintaxis de búsqueda'
  },

  // Error handling
  errors: {
    // Error types
    serviceInit: {
      title: 'Error de Inicialización del Servicio',
      message: 'El servicio de búsqueda no pudo iniciarse correctamente, podría ser un problema de compatibilidad del navegador',
      solutions: [
        'Actualizar la página e intentar de nuevo',
        'Verificar si tu navegador soporta IndexedDB',
        'Limpiar caché y datos del navegador',
        'Reiniciar tu navegador'
      ]
    },
    search: {
      title: 'Error del Servicio de Búsqueda',
      message: 'La funcionalidad de búsqueda no está disponible temporalmente, podría ser un problema de indexación de datos',
      solutions: [
        'Verificar si las palabras clave de búsqueda son válidas',
        'Intentar simplificar los términos de búsqueda',
        'Esperar unos segundos e intentar de nuevo',
        'Actualizar la página para empezar de nuevo'
      ]
    },
    network: {
      title: 'Problema de Conexión de Red',
      message: 'No se puede conectar a los servicios requeridos, por favor verificar el estado de tu red',
      solutions: [
        'Verificar si la conexión de red está funcionando',
        'Actualizar la página e intentar de nuevo',
        'Intentar más tarde'
      ]
    },
    database: {
      title: 'Problema de Almacenamiento de Datos',
      message: 'Error de acceso a la base de datos local, podría necesitarse limpieza de datos',
      solutions: [
        'Verificar el espacio de almacenamiento del navegador',
        'Limpiar datos de la extensión y empezar de nuevo',
        'Restablecer datos en configuración de la extensión',
        'Contactar soporte técnico'
      ]
    },
    unknown: {
      title: 'Error Desconocido',
      message: 'Ocurrió un error inesperado, por favor intenta actualizar la página',
      solutions: [
        'Actualizar la página e intentar de nuevo',
        'Reiniciar tu navegador',
        'Verificar si la extensión está funcionando correctamente',
        'Contactar soporte si el problema persiste'
      ]
    },
    // Error actions
    reload: '🔄 Recargar',
    closeError: '✕ Cerrar Error',
    openSettings: '⚙️ Abrir Configuración',
    solutionsTitle: '💡 Soluciones'
  },

  // Empty state
  empty: {
    title: 'Comienza a buscar en tu historial',
    description: 'Ingresa palabras clave arriba para encontrar rápidamente páginas que has visitado antes',
    features: {
      fuzzyMatch: 'Coincidencia difusa inteligente',
      multiFilter: 'Filtrado multidimensional',
      fastResponse: 'Respuesta en milisegundos'
    },
    searchTips: {
      title: 'Consejos de Búsqueda',
      items: [
        'Soporta búsqueda mixta en chino e inglés',
        'Puede buscar títulos de página, URLs y contenido',
        'Usar filtros para resultados precisos'
      ]
    },
    keyboardShortcuts: {
      title: '⌨️ Atajos de Teclado',
      focusSearch: 'Enfocar búsqueda',
      clearSearch: 'Limpiar búsqueda',
      browseResults: 'Navegar resultados',
      openPage: 'Abrir página',
      quickOpen: 'Apertura rápida'
    },
    searchExamples: {
      title: 'Prueba estos ejemplos de búsqueda',
      basic: {
        text: 'React JavaScript',
        description: 'Búsqueda básica'
      },
      exact: {
        text: '"mejores prácticas"',
        description: 'Coincidencia exacta'
      },
      site: {
        text: 'site:github.com',
        description: 'Específico del sitio'
      },
      exclude: {
        text: 'Vue -tutorial',
        description: 'Búsqueda de exclusión'
      }
    }
  },

  // Options page
  options: {
    title: 'Centro de Configuración',
    pageNotFound: 'Página no encontrada',
    selectOption: 'Por favor selecciona una opción de la navegación izquierda.',
    loadingConfig: 'Cargando centro de configuración...',
    
    // App settings
    app: {
      title: 'App Settings',
      description: 'Configure general application settings.',
      settings: {
        theme: {
          label: 'Theme',
          description: 'Choose between light and dark themes.',
          options: {
            light: 'Light',
            dark: 'Dark',
            system: 'System'
          }
        },
        density: {
          label: 'Density',
          description: 'Adjust the information density of the interface.',
          options: {
            compact: 'Compact',
            default: 'Default',
            comfortable: 'Comfortable'
          }
        },
        language: {
          label: 'Language',
          description: 'Set the display language of the application.'
        }
      }
    },

    // Language settings
    languageSettings: {
      description: 'Configurar el idioma de la interfaz y las preferencias de localización.',
      interfaceLanguage: 'Idioma de la Interfaz',
      interfaceDescription: 'Elegir el idioma para la interfaz de la extensión.',
      note: 'Los cambios de idioma tendrán efecto inmediatamente sin requerir reinicio.'
    },

    // History Management
    historyManagement: {
      title: '📚 Gestión de Historial',
      description: 'Ver, buscar y gestionar tu historial de navegación',
      loading: 'Cargando registros de historial...',
      stats: {
        totalPages: 'Páginas totales: {{count}}',
        totalDomains: 'Dominios únicos: {{count}}',
        dateRange: 'Desde {{oldest}} hasta {{newest}}',
        totalSize: 'Tamaño total: {{size}} MB'
      },
      search: {
        placeholder: 'Buscar títulos, URLs y contenido...',
        noResults: 'No se encontró historial coincidente',
        searching: 'Buscando...'
      },
      sorting: {
        label: 'Ordenar:',
        options: {
          visitTimeDesc: 'Última visita',
          visitTimeAsc: 'Primera visita',
          lastUpdatedDesc: 'Actualizado recientemente',
          lastUpdatedAsc: 'Actualizado temprano',
          domainAsc: 'Ordenar por dominio (A-Z)',
          idDesc: 'Orden por ID'
        }
      },
      actions: {
        selectAll: 'Seleccionar todo',
        deselectAll: 'Deseleccionar todo',
        deleteSelected: 'Eliminar seleccionados',
        deleteConfirm: '¿Estás seguro de que quieres eliminar "{{title}}"?',
        deleteSuccess: 'Eliminación exitosa',
        deleteError: 'Error al eliminar, por favor intenta de nuevo',
        exportSelected: 'Exportar seleccionados',
        refreshList: 'Actualizar lista'
      },
      item: {
        visitTime: 'Visitado {{time}}',
        readingTime: 'Leído por {{duration}}',
        accessCount: 'Accedido {{count}} veces',
        accessCountTooltip: 'Número de accesos',
        lastVisitTooltip: 'Hora de última visita',
        contentSizeTooltip: 'Tamaño del contenido',
        delete: 'Eliminar este registro',
        deleteAriaLabel: 'Eliminar {{title}}',
        openInNewTab: 'Abrir en nueva pestaña',
        sizeKB: '{{size}} KB'
      },
      emptyState: {
        noHistory: 'No se encontraron registros de historial',
        noSearchResults: 'No se encontraron páginas que contengan "{{query}}"',
        startBrowsing: 'Comienza a navegar la web para construir tu historial'
      }
    },

    // API Key Management
    apiKeys: {
      title: '🔑 Gestión de Claves API',
      description: 'Configura las claves API de tu proveedor de servicios de IA y habilita el modo BYOK (Bring Your Own Key)',
      loading: 'Cargando configuración de claves API...',
      privacy: {
        title: 'Protección de Privacidad',
        points: [
          'Todas las claves API están cifradas con AES-256 y almacenadas localmente',
          'Las claves se conectan directamente a los proveedores de servicios de IA correspondientes, no a través de nuestros servidores',
          'Tienes control completo sobre la seguridad de tus datos y claves'
        ]
      },
      providers: {
        openai: {
          name: 'OpenAI',
          description: 'GPT-4, GPT-3.5 y otros modelos',
          setupGuide: 'Crear clave API en la plataforma OpenAI',
          keyFormat: 'sk-...'
        },
        anthropic: {
          name: 'Anthropic',
          description: 'Modelos de la serie Claude',
          setupGuide: 'Crear clave API en Anthropic Console',
          keyFormat: 'sk-ant-...'
        },
        deepseek: {
          name: 'DeepSeek',
          description: 'Modelos DeepSeek Chat',
          setupGuide: 'Crear clave API en la plataforma DeepSeek',
          keyFormat: 'sk-...'
        },
        google: {
          name: 'Google AI',
          description: 'Modelos de la serie Gemini',
          setupGuide: 'Crear clave API en Google AI Studio',
          keyFormat: 'AI...'
        },
        azure: {
          name: 'Azure OpenAI',
          description: 'Modelos OpenAI alojados en Azure',
          setupGuide: 'Configurar recurso OpenAI en Azure Portal',
          keyFormat: 'Requiere endpoint y clave'
        },
        litellm: {
          name: 'LiteLLM',
          description: 'Interfaz unificada que soporta múltiples proveedores de modelos',
          setupGuide: 'Configurar endpoint proxy LiteLLM',
          keyFormat: 'Depende del proveedor específico'
        }
      },
      modal: {
        title: 'Configurar Clave API de {{providerName}}',
        apiKeyLabel: 'Clave API',
        apiKeyPlaceholder: 'Ingresa tu clave API de {{providerName}}',
        endpointLabel: 'Endpoint API',
        endpointPlaceholder: 'https://tu-recurso.openai.azure.com/',
        securityNotice: '🔐 Las claves se almacenarán de forma segura localmente usando el algoritmo de cifrado AES-256, solo para uso de la extensión'
      },
      messages: {
        invalidKey: 'Por favor ingresa una clave API válida',
        saveSuccess: 'Clave API guardada de forma segura',
        saveError: 'Error al guardar, por favor intenta de nuevo',
        removeConfirm: '¿Estás seguro de que quieres eliminar la clave API para {{providerName}}?',
        removeSuccess: 'Clave API eliminada',
        removeError: 'Error al eliminar, por favor intenta de nuevo',
        testSuccess: 'Verificación de clave API exitosa',
        testError: 'Error en la verificación de clave API',
        keyNotFound: 'Clave API no encontrada'
      }
    },

    // Blacklist Management
    blacklistManagement: {
      title: 'Lista Negra',
      description: 'Gestionar dominios de sitios web que no desea indexar',
      loading: 'Cargando...',
      
      // Header stats
      stats: {
        totalDomains: 'Dominios totales: {{count}}',
        wildcardDomains: 'Comodines: {{count}}'
      },
      
      // Search functionality
      search: {
        placeholder: 'Buscar dominios y razones...'
      },
      
      // Add domain modal and form
      addDomain: {
        modalTitle: 'Agregar Nuevo Dominio Bloqueado',
        domainLabel: 'Dominio',
        domainPlaceholder: 'example.com',
        domainPlaceholderWildcard: '*.example.com',
        reasonLabel: 'Razón (opcional)',
        reasonPlaceholder: 'Explicar razón (opcional)',
        wildcardLabel: 'Coincidencia comodín (coincide con todos los subdominios)',
        submitButton: 'Confirmar Agregar',
        submitButtonLoading: 'Agregando...',
        cancelButton: 'Cancelar',
        
        // Validation errors
        errors: {
          domainRequired: 'El dominio no puede estar vacío',
          domainInvalid: 'Por favor ingrese un formato de dominio válido',
          wildcardInvalid: 'Los dominios comodín deben comenzar con *.'
        },
        
        // Status messages
        addFailed: 'Error al agregar, por favor intente de nuevo'
      },
      
      // Domain list and items
      domainList: {
        noReason: 'Ninguna',
        exactMatch: 'Exacto',
        wildcardMatch: 'Comodín',
        exactMatchTooltip: 'Coincidencia exacta',
        wildcardMatchTooltip: 'Coincidencia comodín',
        addedTimeTooltip: 'Hora agregada: {{time}}',
        removeTooltip: 'Eliminar de la lista negra',
        removeAriaLabel: 'Eliminar {{domain}}'
      },
      
      // Bulk actions
      bulkActions: {
        clearAll: 'Limpiar Todo',
        clearAllTooltip: 'Limpiar todos los dominios bloqueados',
        addDomain: 'Agregar Dominio',
        addDomainTooltip: 'Agregar nuevo dominio bloqueado'
      },
      
      // Confirmation dialogs
      confirmations: {
        removeDomain: '¿Está seguro de que desea eliminar "{{domain}}" de la lista negra? Esta operación no se puede deshacer.',
        clearAll: '¿Está seguro de que desea limpiar toda la lista negra ({{count}} entradas)? Esta operación no se puede deshacer.',
        deleteFailed: 'Error al eliminar, por favor intente de nuevo',
        clearFailed: 'Error al limpiar, por favor intente de nuevo'
      },
      
      // Empty states
      emptyState: {
        noResults: 'No se encontraron resultados',
        noResultsDescription: 'No se encontraron dominios relacionados con "{{query}}"',
        empty: 'La lista negra está vacía',
        emptyDescription: 'Agregue dominios a la lista negra, las páginas relacionadas no serán indexadas',
        addFirstDomain: 'Agregar primer dominio'
      },
      
      // Help tooltip
      help: {
        title: 'Instrucciones de Uso',
        exactMatch: 'Coincidencia exacta: Solo coincide con el dominio especificado, como "example.com"',
        wildcardMatch: 'Coincidencia comodín: Coincide con el dominio y todos sus subdominios, como "*.example.com"',
        autoEffect: 'Efecto automático: Los dominios agregados a la lista negra no serán indexados por nuevas páginas',
        existingData: 'Datos existentes: La lista negra no afecta las páginas ya indexadas, necesita eliminación manual'
      }
    },
    
    // Navigation items
    navigation: {
      historyManagement: {
        label: 'Registros de Historial',
        description: 'Ver, buscar y gestionar tu historial de navegación'
      },
      blacklistManagement: {
        label: 'Lista Negra',
        description: 'Gestionar dominios de sitios web que no quieres indexar'
      },
      dataBackup: {
        label: 'Importar y Exportar',
        description: 'Exportar e importar tus datos de historial'
      },
      searchSettings: {
        label: 'Configuración de Búsqueda',
        description: 'Configurar comportamiento y preferencias de búsqueda'
      },
      apiKeyManagement: {
        label: 'Gestión de Claves API',
        description: 'Gestionar claves API para proveedores de servicios de IA'
      },
      readingAssistant: {
        label: 'Asistente de Lectura',
        description: 'Configurar asistente de lectura inteligente y funciones de resumen de IA'
      },
      languageSettings: {
        label: 'Idioma',
        description: 'Cambiar idioma de interfaz y configuración de localización'
      },
      about: {
        label: 'Acerca de',
        description: 'Información de versión y ayuda de uso'
      }
    },

    // Common options translations
    common: {
      loading: 'Cargando...',
      saving: 'Guardando...',
      saved: 'Configuración guardada',
      saveFailed: 'Error al guardar, por favor intenta de nuevo',
      resetToDefaults: 'Restablecer valores predeterminados',
      resetConfirm: '¿Estás seguro de que quieres restablecer la configuración predeterminada?',
      configure: 'Configurar',
      configured: 'Configurado',
      unconfigured: 'No configurado',
      testConnection: 'Probar conexión',
      removeKey: 'Eliminar clave',
      getKey: 'Obtener clave',
      saveKey: 'Guardar clave',
      cancel: 'Cancelar',
      close: '×',
      enabled: 'Habilitado',
      disabled: 'Deshabilitado',
      version: 'Versión {{version}}',
      units: {
        items: 'elementos',
        ms: 'ms',
        seconds: 'segundos',
        minutes: 'minutos',
        mb: 'MB',
        percent: '%'
      }
    },

    // Search Settings
    search: {
      title: '⚙️ Configuración de Búsqueda',
      description: 'Configurar parámetros de comportamiento de búsqueda semántica y por palabras clave',
      loading: 'Cargando configuración de búsqueda...',
      sections: {
        results: {
          title: '📊 Resultados de Búsqueda',
          description: 'Controlar cantidad y visualización de resultados de búsqueda'
        },
        traditional: {
          title: '🔍 Búsqueda Tradicional',
          description: 'Configuración de búsqueda tradicional basada en coincidencia de palabras clave'
        },
        keyword: {
          title: '🔍 Búsqueda por Palabras Clave',
          description: 'Configuración de búsqueda de coincidencia tradicional por palabras clave'
        },
        indexing: {
          title: '📚 Indexación de Contenido',
          description: 'Configuración de indexación automática de contenido de páginas'
        },
        userExperience: {
          title: '🎯 Experiencia de Usuario',
          description: 'Configuración de interfaz de búsqueda y experiencia de interacción'
        }
      },
      settings: {
        maxResults: {
          label: 'Máximo de resultados de búsqueda',
          description: 'Número máximo de resultados devueltos en una sola búsqueda'
        },
        searchTimeout: {
          label: 'Tiempo de espera de búsqueda',
          description: 'Tiempo máximo de espera para solicitudes de búsqueda'
        },
        traditionalSearchWeight: {
          label: 'Peso de búsqueda tradicional',
          description: 'Relación de peso de búsqueda tradicional en búsqueda híbrida'
        },
        enableKeywordSearch: {
          label: 'Habilitar búsqueda por palabras clave',
          description: 'Usar búsqueda de coincidencia tradicional por palabras clave'
        },
        fuzzySearchThreshold: {
          label: 'Umbral de búsqueda difusa',
          description: 'Controlar sensibilidad de coincidencia difusa (menor es más estricto)'
        },
        keywordSearchWeight: {
          label: 'Peso de búsqueda por palabras clave',
          description: 'Relación de peso de búsqueda por palabras clave en búsqueda híbrida'
        },
        enableIndexing: {
          label: 'Habilitar indexación automática',
          description: 'Indexar automáticamente el contenido de páginas visitadas'
        },
        indexingDelay: {
          label: 'Retraso de indexación',
          description: 'Retraso después de cargar la página antes de comenzar la indexación'
        },
        enableSearchHistory: {
          label: 'Habilitar historial de búsqueda',
          description: 'Recordar consultas de búsqueda recientes'
        },
        enableAutoComplete: {
          label: 'Habilitar autocompletado',
          description: 'Mostrar sugerencias de búsqueda mientras escribes'
        },
        enableDebugMode: {
          label: 'Habilitar modo de depuración',
          description: 'Mostrar información detallada de depuración de búsqueda'
        }
      }
    },

    // Data Backup
    dataBackup: {
      title: '💾 Copia de Seguridad e Importación de Datos',
      description: 'Exportar e importar datos del historial de navegación',
      loading: 'Cargando interfaz de copia de seguridad de datos...',
      stats: {
        title: 'Estadísticas de Datos',
        totalPages: 'Total de páginas: {{count}}',
        totalBlacklist: 'Dominios en lista negra: {{count}}',
        lastUpdate: 'Última actualización: {{time}}'
      },
      export: {
        title: 'Exportar Datos',
        description: 'Exportar todo tu historial de navegación y configuraciones como un archivo JSON',
        button: 'Exportar Todos los Datos',
        processing: 'Exportando...',
        progress: 'Exportando... {{progress}}%',
        success: 'Datos exportados exitosamente',
        error: 'Error al exportar: {{error}}'
      },
      import: {
        title: 'Importar Datos',
        description: 'Importar archivo de datos exportado previamente',
        button: 'Seleccionar Archivo de Importación',
        processing: 'Procesando importación...',
        success: 'Importación completada exitosamente',
        error: 'Error al importar: {{error}}',
        invalidFormat: 'Formato de archivo de respaldo inválido',
        stats: {
          pagesImported: 'Páginas importadas: {{count}}',
          blacklistImported: 'Lista negra importada: {{count}}',
          pagesSkipped: 'Páginas omitidas: {{count}}',
          blacklistSkipped: 'Lista negra omitida: {{count}}'
        }
      },
      warnings: {
        exportSize: 'El archivo de exportación puede ser grande dependiendo del tamaño de tu historial',
        importOverwrite: 'La importación se fusionará con datos existentes, los duplicados serán omitidos',
        backupFirst: 'Se recomienda respaldar los datos actuales antes de importar'
      },
      dangerZone: {
        title: 'Zona de Peligro',
        description: 'Limpiar todos los datos eliminará permanentemente todo el historial de navegación y la lista negra. Esta operación no se puede deshacer. Asegúrese de haber respaldado los datos importantes antes de continuar.',
        clearAllButton: '🗑️ Limpiar Todos los Datos',
        clearAllConfirm: '¿Estás seguro de que quieres limpiar todos los datos? Esto eliminará permanentemente todo el historial de navegación y la lista negra. ¡Esta operación no se puede deshacer!',
        clearAllDoubleConfirm: 'Por favor confirma nuevamente: Esto eliminará permanentemente todos los datos, incluyendo el historial de navegación y la lista negra. ¿Estás seguro de que quieres continuar?',
        clearAllSuccess: 'Todos los datos han sido eliminados',
        clearAllError: 'Error al limpiar, por favor intenta de nuevo'
      },
      importResults: {
        title: 'Resultados de Importación',
        pagesImported: 'Historial importado:',
        blacklistImported: 'Lista negra importada:',
        pagesSkipped: 'Historial omitido:',
        blacklistSkipped: 'Lista negra omitida:',
        entries: '{{count}} entradas',
        importErrors: 'Errores de importación:',
        moreErrors: '... y {{count}} errores más'
      }
    },

    // Reading Assistant
    readingAssistant: {
      title: '📖 Asistente de Lectura',
      description: 'Configurar asistente de lectura inteligente y funciones de resumen de IA para mejorar tu experiencia de lectura',
      loading: 'Cargando configuración del asistente de lectura...',
      sections: {
        detection: {
          title: '🕐 Detección de Lectura',
          description: 'Detectar automáticamente tu comportamiento y duración de lectura'
        },
        notifications: {
          title: '🔔 Recordatorios de Lectura',
          description: 'Mostrar recordatorios no intrusivos durante sesiones de lectura prolongadas'
        },
        aiSummary: {
          title: '🤖 Resumen de IA',
          description: 'Usar IA para generar resúmenes inteligentes para artículos largos'
        },
        general: {
          title: '⚙️ Configuración General',
          description: 'Otras configuraciones relacionadas con el asistente de lectura'
        }
      },
      settings: {
        enableReadingDetection: {
          label: 'Habilitar detección de lectura',
          description: 'Monitorear tiempo de permanencia en página y estado activo'
        },
        minimumReadingTime: {
          label: 'Tiempo mínimo de lectura',
          description: 'Las visitas más cortas que este tiempo no se registran como lectura'
        },
        enableToastNotifications: {
          label: 'Habilitar recordatorios de lectura',
          description: 'Mostrar recordatorios de duración de lectura en la esquina inferior derecha'
        },
        readingTimeThreshold: {
          label: 'Tiempo de activación del recordatorio',
          description: 'Mostrar recordatorio después de alcanzar esta duración de lectura'
        },
        enableAutoSummary: {
          label: 'Habilitar resumen de IA',
          description: 'Sugerir automáticamente generar resúmenes para artículos largos'
        },
        summaryTriggerThreshold: {
          label: 'Tiempo de activación del resumen',
          description: 'Sugerir generar resumen después de leer durante esta duración'
        },
        summaryPosition: {
          label: 'Posición del resumen',
          description: 'Posición de visualización del panel de resumen en la página',
          options: {
            topRight: 'Superior derecha',
            topLeft: 'Superior izquierda',
            bottomRight: 'Inferior derecha',
            bottomLeft: 'Inferior izquierda'
          }
        },
        summaryLanguage: {
          label: 'Idioma del resumen',
          description: 'Preferencia de idioma para la generación de resúmenes',
          options: {
            auto: 'Detección automática',
            zh: 'Chino',
            en: 'Inglés'
          }
        },
        summaryLength: {
          label: 'Longitud del resumen',
          description: 'Nivel de detalle del resumen generado',
          options: {
            short: 'Breve (1-2 oraciones)',
            medium: 'Medio (1 párrafo)',
            long: 'Detallado (múltiples párrafos)'
          }
        },
        enableKeyboardShortcuts: {
          label: 'Habilitar atajos de teclado',
          description: 'Usar Ctrl+Shift+S para generar rápidamente un resumen'
        }
      },
      apiNotice: {
        title: '⚠️ Clave API Requerida',
        description: 'Las funciones de resumen de IA requieren configurar claves API de proveedores de servicios LLM. Por favor, ve a la página {{apiKeyManagement}} para configurar.',
        configureButton: 'Configurar Claves API'
      }
    },

    // About
    about: {
      title: 'Recall',
      version: 'Versión {{version}}',
      tagline: 'Resuelve el problema de los usuarios "lo he visto pero no lo encuentro", haciendo que tu historial de navegación sea realmente útil a través de búsqueda inteligente de texto completo.',
      features: {
        title: 'Características Principales',
        items: {
          fullTextSearch: {
            title: 'Búsqueda de Texto Completo',
            description: 'No solo títulos, sino contenido completo de la página.'
          },
          fastResponse: {
            title: 'Respuesta Ultra-Rápida',
            description: 'Resultados de búsqueda en tiempo real, respuesta en milisegundos.'
          },
          privacyFirst: {
            title: 'Privacidad Primero',
            description: 'Todos los datos almacenados localmente, nunca subidos a la nube.'
          },
          smartMatching: {
            title: 'Coincidencia Inteligente',
            description: 'Soporta búsqueda difusa y sintaxis de consulta avanzada.'
          }
        }
      },
      privacy: {
        title: 'Compromiso de Privacidad',
        description: 'Creemos firmemente que tus datos te pertenecen. Recall almacena de forma segura todos los índices del historial de navegación localmente en tu navegador. Nunca subimos tus datos a ningún servidor. Este proyecto es completamente de código abierto, y puedes revisar el código en cualquier momento para verificar nuestro compromiso.'
      },
      links: {
        title: 'Enlaces Útiles',
        items: {
          github: '⭐ Danos una estrella en GitHub',
          guide: '❓ Ver guía de uso',
          review: '📝 Califícanos en Chrome Store'
        }
      },
      acknowledgements: 'Gracias a {{readability}} y {{fusejs}} por su poderoso soporte.',
      copyright: '© 2025 Recall. Made with ❤️ by penwyp.'
    }
  },

  // Search Bar
  searchBar: {
    suggestions: 'Sugerencias de búsqueda',
    noSuggestions: 'No hay sugerencias'
  },

  // Status Bar
  statusBar: {
    systemStatus: 'Estado del Sistema',
    normalOperation: 'Funcionando Normalmente',
    needsAttention: 'Necesita Atención',
    justNow: 'Ahora mismo',
    minutesAgo: 'hace {{count}} minutos',
    hoursAgo: 'hace {{count}} horas',
    daysAgo: 'hace {{count}} días',
    unknown: 'Desconocido',
    veryFast: 'Muy Rápido',
    fast: 'Rápido',
    normal: 'Normal',
    slow: 'Lento',
    systemDetails: 'Detalles del Sistema',
    lastUpdate: 'Última Actualización',
    storageInfo: 'Información de Almacenamiento',
    performanceMetrics: 'Métricas de Rendimiento',
    dataManagement: 'Gestión de Datos',
    backupData: 'Respaldar Datos',
    pages: 'Páginas',
    showDetails: 'Mostrar Detalles',
    hideDetails: 'Ocultar Detalles',
    refreshStats: 'Actualizar Estadísticas',
    openSettings: 'Abrir Configuración',
    indexSize: 'Tamaño del Índice',
    totalPages: 'Total de Páginas',
    searchSpeed: 'Velocidad de Búsqueda',
    responseTime: 'Tiempo de Respuesta'
  },

  // Filters
  filters: {
    all: 'Todo',
    today: 'Hoy',
    thisWeek: 'Esta Semana',
    thisMonth: 'Este Mes',
    relevance: 'Relevancia',
    time: 'Tiempo',
    accessCount: 'Número de Accesos'
  },

  // Syntax Help
  syntaxHelp: {
    title: 'Sintaxis de Búsqueda Avanzada',
    close: 'Cerrar ayuda',
    intro: 'Usa la siguiente sintaxis para buscar precisamente en tu historial de navegación:',
    examples: {
      basic: 'Buscar páginas que contengan React y JavaScript',
      exact: 'Coincidencia exacta de frases (debe coincidir completamente)',
      exclude: 'Excluir resultados que contengan palabras específicas',
      site: 'Buscar solo páginas de sitios web específicos',
      complex: 'Consulta compuesta: contiene React, coincidencia exacta "component lifecycle", excluye class, limitado a reactjs.org'
    },
    tips: {
      title: 'Consejos de Uso',
      items: {
        '0': 'Se pueden combinar múltiples sintaxis',
        '1': 'El contenido entre comillas será coincidido exactamente',
        '2': 'El signo menos (-) debe estar pegado a la palabra a excluir',
        '3': 'site: soporta coincidencia de subdominios',
        '4': 'La búsqueda no distingue entre mayúsculas y minúsculas'
      }
    },
    startSearch: 'Comenzar Búsqueda',
    viewHelp: 'Ver ayuda de sintaxis de búsqueda',
    syntax: 'Sintaxis'
  },


  // Manifest localization
  manifest: {
    name: 'Recall',
    description: 'Extensión de gestión de conocimiento y búsqueda de historial de navegador con IA',
    actionTitle: 'Recall - Búsqueda Inteligente de Historial'
  }
} as const;