/**
 * 状态栏组件 - 重新设计的UI版本
 *
 * 显示搜索统计信息、性能指标和扩展状态
 * 提供快速访问设置和系统状态的入口
 */

import "./StatusBar.css"

import React, { useState, useEffect, useCallback } from "react"
import { dbService, searchService } from "../../services"
import { I18nManager } from "../../i18n/I18nManager"

/**
 * 状态栏属性接口
 */
interface StatusBarProps {
  /** 搜索结果总数 */
  totalResults: number
  /** 搜索耗时（毫秒） */
  searchTime: number
  /** 是否正在加载 */
  isLoading?: boolean
}

/**
 * 扩展统计信息接口
 */
interface ExtensionStats {
  totalPages: number
  indexSize: number
  lastUpdate: number
  isHealthy: boolean
}

/**
 * 状态栏组件
 */
export const StatusBar: React.FC<StatusBarProps> = ({ totalResults, searchTime, isLoading = false }) => {
  const [stats, setStats] = useState<ExtensionStats>({
    totalPages: 0,
    indexSize: 0,
    lastUpdate: 0,
    isHealthy: true,
  })
  const [showDetails, setShowDetails] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // i18n Management
  const [i18nManager] = useState(() => I18nManager.getInstance())
  const [, forceUpdate] = useState({})

  // Translation helper function
  const t = useCallback((key: string, interpolations?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolations)
  }, [i18nManager])

  /**
   * 加载扩展统计信息
   */
  const loadStats = async () => {
    try {
      const [storageInfo, indexStats, healthCheck] = await Promise.all([
        dbService.getStorageInfo(),
        searchService.getIndexStats(),
        dbService.healthCheck(),
      ])

      setStats({
        totalPages: storageInfo.pagesCount,
        indexSize: indexStats.totalPages,
        lastUpdate: indexStats.lastUpdate,
        isHealthy: healthCheck.isHealthy,
      })
    } catch (error) {
      console.error("Failed to load extension stats:", error)
      setStats((prev) => ({ ...prev, isHealthy: false }))
    }
  }

  /**
   * 刷新统计信息
   */
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await loadStats()
    setTimeout(() => setIsRefreshing(false), 500) // 给用户反馈时间
  }

  /**
   * 加载扩展统计信息
   */
  useEffect(() => {
    loadStats()

    // 定期更新统计信息
    const interval = setInterval(loadStats, 30000) // 每30秒更新一次

    return () => clearInterval(interval)
  }, [])

  /**
   * 监听语言变化
   */
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({})
    }

    // 监听语言变化事件
    i18nManager.addLanguageChangeListener(handleLanguageChange)

    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange)
    }
  }, [i18nManager])

  /**
   * 格式化时间
   */
  const formatTime = (ms: number): string => {
    if (ms < 1000) {
      return `${ms}ms`
    }
    return `${(ms / 1000).toFixed(1)}s`
  }

  /**
   * 格式化数字
   */
  const formatNumber = (num: number): string => {
    if (num < 1000) {
      return num.toString()
    }
    if (num < 1000000) {
      return `${(num / 1000).toFixed(1)}K`
    }
    return `${(num / 1000000).toFixed(1)}M`
  }

  /**
   * 格式化最后更新时间
   */
  const formatLastUpdate = (timestamp: number): string => {
    if (!timestamp) return t("statusBar.unknown")

    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return t("statusBar.justNow")
    if (minutes < 60) return t("statusBar.minutesAgo", { count: minutes })
    if (hours < 24) return t("statusBar.hoursAgo", { count: hours })
    return t("statusBar.daysAgo", { count: days })
  }

  /**
   * 获取性能等级
   */
  const getPerformanceLevel = (ms: number): { level: string; color: string; icon: string } => {
    if (ms < 100) return { level: t("statusBar.veryFast"), color: "#10b981", icon: "⚡" }
    if (ms < 500) return { level: t("statusBar.fast"), color: "#3b82f6", icon: "🚀" }
    if (ms < 1000) return { level: t("statusBar.normal"), color: "#f59e0b", icon: "⏱️" }
    return { level: t("statusBar.slow"), color: "#ef4444", icon: "🐌" }
  }

  /**
   * 获取健康状态信息
   */
  const getHealthStatus = () => {
    if (stats.isHealthy) {
      return {
        status: t("statusBar.normalOperation"),
        color: "#10b981",
        icon: "✅",
        description: "所有功能正常",
      }
    }
    return {
      status: t("statusBar.needsAttention"),
      color: "#ef4444",
      icon: "⚠️",
      description: "可能存在问题",
    }
  }

  const performanceInfo = getPerformanceLevel(searchTime)
  const healthInfo = getHealthStatus()

  return (
    <div className="hg-status-bar">
      {/* 主要状态信息 */}
      <div className="hg-status-main">
        <div className="hg-status-content">
          {isLoading ? (
            <div className="hg-status-loading">
              <div className="hg-loading-spinner" />
              <span className="hg-loading-text">{t("search.searching")}</span>
            </div>
          ) : (
            <div className="hg-status-metrics">
              {/* 搜索结果统计 */}
              {totalResults > 0 ? (
                <div className="hg-metric-group hg-results-metric">
                  <div className="hg-metric-icon">📊</div>
                  <div className="hg-metric-content">
                    <div className="hg-metric-value">{formatNumber(totalResults)}</div>
                    <div className="hg-metric-label">{t("search.results")}</div>
                  </div>
                </div>
              ) : null}

              {/* 搜索性能 */}
              {totalResults > 0 ? (
                <div className="hg-metric-group hg-performance-metric">
                  <div className="hg-metric-icon" style={{ color: performanceInfo.color }}>
                    {performanceInfo.icon}
                  </div>
                  <div className="hg-metric-content">
                    <div className="hg-metric-value">{formatTime(searchTime)}</div>
                    <div className="hg-metric-label">{performanceInfo.level}</div>
                  </div>
                </div>
              ) : null}

              {/* 数据库状态 */}
              <div className="hg-metric-group hg-database-metric">
                <div className="hg-metric-icon">📚</div>
                <div className="hg-metric-content">
                  <div className="hg-metric-value">{formatNumber(stats.totalPages)}</div>
                  <div className="hg-metric-label">{t("statusBar.pages")}</div>
                </div>
              </div>

              {/* 系统健康状态 */}
              <div className="hg-metric-group hg-health-metric">
                <div className="hg-metric-icon" style={{ color: healthInfo.color }}>
                  {healthInfo.icon}
                </div>
                <div className="hg-metric-content">
                  <div className="hg-metric-value">{healthInfo.status}</div>
                  <div className="hg-metric-label">{t("statusBar.systemStatus")}</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮组 */}
        <div className="hg-status-actions">
          {/* 详情切换按钮 */}
          <button
            type="button"
            onClick={() => setShowDetails(!showDetails)}
            className={`hg-action-btn hg-details-btn ${showDetails ? "active" : ""}`}
            title={showDetails ? t("statusBar.hideDetails") : t("statusBar.showDetails")}
          >
            <span className={`hg-expand-icon ${showDetails ? "expanded" : ""}`}>📈</span>
          </button>

          {/* 刷新按钮 */}
          <button
            type="button"
            onClick={handleRefresh}
            className={`hg-action-btn hg-refresh-btn ${isRefreshing ? "refreshing" : ""}`}
            title={t("statusBar.refreshStats")}
            disabled={isRefreshing}
          >
            <span className={`hg-refresh-icon ${isRefreshing ? "spinning" : ""}`}>🔄</span>
          </button>

          {/* 设置按钮 */}
          <button
            type="button"
            onClick={() => {
              chrome.tabs.create({ url: chrome.runtime.getURL("options.html") })
            }}
            className="hg-action-btn hg-settings-btn"
            title={t("statusBar.openSettings")}
          >
            <span className="hg-settings-icon">⚙️</span>
          </button>
        </div>
      </div>

      {/* 详细信息面板 */}
      {showDetails && (
        <div className="hg-status-details">
          <div className="hg-details-header">
            <h4 className="hg-details-title">{t("statusBar.systemDetails")}</h4>
            <div className="hg-last-update">{t("statusBar.lastUpdate")}: {formatLastUpdate(stats.lastUpdate)}</div>
          </div>

          <div className="hg-details-grid">
            <div className="hg-detail-card">
              <div className="hg-detail-header">
                <span className="hg-detail-icon">💾</span>
                <span className="hg-detail-title">{t("statusBar.storageInfo")}</span>
              </div>
              <div className="hg-detail-stats">
                <div className="hg-detail-item">
                  <span className="hg-detail-label">{t("statusBar.indexSize")}</span>
                  <span className="hg-detail-value">{formatNumber(stats.indexSize)}</span>
                </div>
                <div className="hg-detail-item">
                  <span className="hg-detail-label">{t("statusBar.totalPages")}</span>
                  <span className="hg-detail-value">{formatNumber(stats.totalPages)}</span>
                </div>
              </div>
            </div>

            <div className="hg-detail-card">
              <div className="hg-detail-header">
                <span className="hg-detail-icon">⚡</span>
                <span className="hg-detail-title">{t("statusBar.performanceMetrics")}</span>
              </div>
              <div className="hg-detail-stats">
                <div className="hg-detail-item">
                  <span className="hg-detail-label">{t("statusBar.searchSpeed")}</span>
                  <span className="hg-detail-value hg-performance-badge" style={{ color: performanceInfo.color }}>
                    {performanceInfo.level}
                  </span>
                </div>
                <div className="hg-detail-item">
                  <span className="hg-detail-label">{t("statusBar.responseTime")}</span>
                  <span className="hg-detail-value">{formatTime(searchTime)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="hg-quick-actions">
            <button
              type="button"
              onClick={() => chrome.tabs.create({ url: chrome.runtime.getURL("options.html#data") })}
              className="hg-quick-action-btn"
            >
              <span className="hg-action-icon">📊</span>
              <span className="hg-action-text">{t("statusBar.dataManagement")}</span>
            </button>

            <button
              type="button"
              onClick={() => chrome.tabs.create({ url: chrome.runtime.getURL("options.html#backup") })}
              className="hg-quick-action-btn"
            >
              <span className="hg-action-icon">💾</span>
              <span className="hg-action-text">{t("statusBar.backupData")}</span>
            </button>

            <button
              type="button"
              onClick={() => chrome.tabs.create({ url: chrome.runtime.getURL("options.html#about") })}
              className="hg-quick-action-btn"
            >
              <span className="hg-action-icon">ℹ️</span>
              <span className="hg-action-text">{t("common.about")}</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

declare const chrome: any
