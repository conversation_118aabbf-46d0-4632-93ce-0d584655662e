/**
 * Advanced Search Syntax Help Component
 * 
 * Displays search syntax help in a tooltip/popover format
 */

import React, { useState, useRef, useEffect } from 'react';
import { I18nManager } from '../../i18n/I18nManager';

/**
 * Syntax help props interface
 */
interface SyntaxHelpProps {
  /** Whether to show the help */
  isVisible: boolean;
  /** Callback when help should be hidden */
  onClose: () => void;
  /** Position relative to trigger element */
  position?: 'top' | 'bottom' | 'left' | 'right';
  /** Additional CSS classes */
  className?: string;
}

/**
 * Get search syntax examples with i18n
 */
const getSyntaxExamples = () => {
  const i18n = I18nManager.getInstance();
  return [
    {
      syntax: 'React JavaScript',
      description: i18n.getTranslation('syntaxHelp.examples.basic'),
      icon: '🔍'
    },
    {
      syntax: '"exact phrase"',
      description: i18n.getTranslation('syntaxHelp.examples.exact'),
      icon: '📝'
    },
    {
      syntax: '-exclude',
      description: i18n.getTranslation('syntaxHelp.examples.exclude'),
      icon: '🚫'
    },
    {
      syntax: 'site:github.com',
      description: i18n.getTranslation('syntaxHelp.examples.site'),
      icon: '🌐'
    },
    {
      syntax: 'React "component lifecycle" -class site:reactjs.org',
      description: i18n.getTranslation('syntaxHelp.examples.complex'),
      icon: '⚡'
    }
  ];
};

/**
 * Syntax help component
 */
export const SyntaxHelp: React.FC<SyntaxHelpProps> = ({
  isVisible,
  onClose,
  position = 'bottom',
  className = ''
}) => {
  const helpRef = useRef<HTMLDivElement>(null);
  const i18n = I18nManager.getInstance();

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (helpRef.current && !helpRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isVisible, onClose]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [isVisible, onClose]);

  if (!isVisible) {
    return null;
  }

  return (
    <div 
      ref={helpRef}
      className={`syntax-help ${position} ${className}`}
      role="dialog"
      aria-label={i18n.getTranslation('syntaxHelp.title')}
    >
      {/* Header */}
      <div className="syntax-help-header">
        <h3 className="syntax-help-title">
          <span className="help-icon">💡</span>
          {i18n.getTranslation('syntaxHelp.title')}
        </h3>
        <button
          type="button"
          onClick={onClose}
          className="syntax-help-close"
          aria-label={i18n.getTranslation('syntaxHelp.close')}
          title={i18n.getTranslation('syntaxHelp.close')}
        >
          ✕
        </button>
      </div>

      {/* Content */}
      <div className="syntax-help-content">
        <p className="syntax-help-intro">
          {i18n.getTranslation('syntaxHelp.intro')}
        </p>

        {/* Examples */}
        <div className="syntax-examples">
          {getSyntaxExamples().map((example, index) => (
            <div key={index} className="syntax-example">
              <div className="example-header">
                <span className="example-icon">{example.icon}</span>
                <code className="example-syntax">{example.syntax}</code>
              </div>
              <p className="example-description">{example.description}</p>
            </div>
          ))}
        </div>

        {/* Tips */}
        <div className="syntax-tips">
          <h4 className="tips-title">💡 {i18n.getTranslation('syntaxHelp.tips.title')}</h4>
          <ul className="tips-list">
            <li>{i18n.getTranslation('syntaxHelp.tips.items.0')}</li>
            <li>{i18n.getTranslation('syntaxHelp.tips.items.1')}</li>
            <li>{i18n.getTranslation('syntaxHelp.tips.items.2')}</li>
            <li>{i18n.getTranslation('syntaxHelp.tips.items.3')}</li>
            <li>{i18n.getTranslation('syntaxHelp.tips.items.4')}</li>
          </ul>
        </div>

        {/* Quick actions */}
        <div className="syntax-actions">
          <button
            type="button"
            className="action-button primary"
            onClick={onClose}
          >
            {i18n.getTranslation('syntaxHelp.startSearch')}
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Syntax help trigger button component
 */
interface SyntaxHelpTriggerProps {
  /** Callback when help should be shown */
  onShow: () => void;
  /** Additional CSS classes */
  className?: string;
  /** Button title */
  title?: string;
}

export const SyntaxHelpTrigger: React.FC<SyntaxHelpTriggerProps> = ({
  onShow,
  className = '',
  title
}) => {
  const i18n = I18nManager.getInstance();
  const defaultTitle = title || i18n.getTranslation('syntaxHelp.viewHelp');
  
  return (
    <button
      type="button"
      onClick={onShow}
      className={`syntax-help-trigger ${className}`}
      title={defaultTitle}
      aria-label={defaultTitle}
    >
      <span className="help-icon">💡</span>
      <span className="help-text">{i18n.getTranslation('syntaxHelp.syntax')}</span>
    </button>
  );
};

/**
 * Combined syntax help with trigger component
 */
interface SyntaxHelpWithTriggerProps {
  /** Position of the help popover */
  position?: 'top' | 'bottom' | 'left' | 'right';
  /** Additional CSS classes */
  className?: string;
}

export const SyntaxHelpWithTrigger: React.FC<SyntaxHelpWithTriggerProps> = ({
  position = 'bottom',
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const handleShow = () => setIsVisible(true);
  const handleClose = () => setIsVisible(false);

  return (
    <div className={`syntax-help-container ${className}`}>
      <SyntaxHelpTrigger onShow={handleShow} />
      <SyntaxHelp
        isVisible={isVisible}
        onClose={handleClose}
        position={position}
      />
    </div>
  );
};
