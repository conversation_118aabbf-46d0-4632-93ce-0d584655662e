/**
 * IndexedDB 数据模型定义
 * 
 * 这个文件定义了 Recall 扩展使用的所有数据结构
 */

/**
 * 页面数据接口
 * 存储用户访问过的网页信息和提取的内容
 */
export interface Page {
  /** 页面唯一标识符 (UUID) */
  id: string;
  
  /** 页面完整URL，作为唯一索引 */
  url: string;
  
  /** 页面标题 */
  title: string;
  
  /** 页面正文内容（通过Readability.js提取） */
  content: string;
  
  /** 网站域名，用于按站点过滤 */
  domain: string;
  
  /** 首次访问时间戳 */
  visitTime: number;
  
  /** 访问次数，每次重新访问时递增 */
  accessCount: number;
  
  /** 最后更新时间戳 */
  lastUpdated: number;
  
  /** 页面语言（可选），用于搜索优化 */
  language?: string;
  
  /** 页面摘要（可选），用于快速预览 */
  summary?: string;

  /** 内容提取状态 */
  contentStatus?: 'extracted' | 'failed' | 'pending' | 'empty';
  
  /** 内容提取错误信息 */
  extractionError?: string;
  
  /** 内容最后更新尝试时间 */
  lastExtractionAttempt?: number;
}

/**
 * 用户设置接口
 * 存储扩展的配置信息
 */
export interface Setting {
  /** 设置项键名 */
  key: string;

  /** 设置项值，支持任意JSON可序列化的数据 */
  value: any;

  /** 设置项创建时间 */
  createdAt: number;

  /** 设置项最后修改时间 */
  updatedAt: number;
}

/**
 * 黑名单条目接口
 * 存储不希望被索引的域名
 */
export interface BlacklistEntry {
  /** 域名（作为主键） */
  domain: string;

  /** 添加时间 */
  createdAt: number;

  /** 添加原因（可选） */
  reason?: string;

  /** 是否启用通配符匹配 */
  isWildcard?: boolean;
}

/**
 * 搜索结果项接口
 * 用于返回搜索结果的标准化格式
 */
export interface SearchResultItem {
  /** 页面数据 */
  page: Page;
  
  /** 搜索相关性分数 (0-1) */
  score: number;
  
  /** 匹配的内容片段，包含高亮标记 */
  highlights: string[];
  
  /** 匹配的字段名称 */
  matchedFields: string[];
  
  /** 可选的元数据信息 */
  metadata?: Record<string, any>;
}

/**
 * 搜索选项接口
 * 用于配置搜索行为
 */
export interface SearchOptions {
  /** 搜索查询字符串 */
  query: string;

  /** 最大返回结果数量 */
  limit?: number;

  /** 时间范围过滤 */
  timeRange?: {
    start: number;
    end: number;
  };

  /** 域名过滤 */
  domains?: string[];

  /** 排除的域名 */
  excludeDomains?: string[];

  /** 排序方式 */
  sortBy?: 'relevance' | 'time' | 'accessCount';

  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
}

/**
 * 分页选项接口
 * 用于配置分页查询行为
 */
export interface PaginationOptions {
  /** 每页数据量，默认50 */
  limit?: number;

  /** 游标位置，用于继续上次查询 */
  cursor?: string | null;

  /** 排序字段 */
  sortBy?: 'visitTime' | 'lastUpdated' | 'domain' | 'id';

  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';

  /** 过滤条件 */
  filter?: PageFilter | null;
}

/**
 * 页面过滤条件接口
 */
export interface PageFilter {
  /** 域名过滤 */
  domains?: string[];

  /** 排除的域名 */
  excludeDomains?: string[];

  /** 时间范围过滤 */
  timeRange?: {
    start: number;
    end: number;
  };

  /** 搜索查询（简单文本匹配） */
  searchQuery?: string;
}

/**
 * 分页结果接口
 */
export interface PaginatedResult<T> {
  /** 当前页数据 */
  items: T[];

  /** 是否还有更多数据 */
  hasMore: boolean;

  /** 下一页游标 */
  nextCursor: string | null;

  /** 总数量（可能是估算值） */
  totalCount: number;
}

/**
 * 数据库配置常量
 */
export const DB_CONFIG = {
  /** 数据库名称 */
  name: 'RecallDB',

  /** 数据库版本 */
  version: 3, // Incremented for new Page fields (contentStatus, extractionError, lastExtractionAttempt)

  /** 对象存储名称 */
  stores: {
    pages: 'pages',
    settings: 'settings',
    blacklist: 'blacklist'
  },

  /** 索引配置 */
  indexes: {
    pages: {
      url: 'url',
      domain: 'domain',
      visitTime: 'visitTime',
      lastUpdated: 'lastUpdated'
    },
    settings: {
      key: 'key'
    },
    blacklist: {
      domain: 'domain',
      createdAt: 'createdAt'
    }
  }
} as const;

/**
 * 预定义的设置键名
 */
export const SETTING_KEYS = {
  /** 搜索配置 */
  SEARCH_CONFIG: 'search_config',
  
  /** 搜索引擎配置 */
  SEARCH_ENGINE_CONFIG: 'search_engine_config',
  
  /** 黑名单域名 */
  BLACKLIST_DOMAINS: 'blacklist_domains',
  
  /** 数据清理策略 */
  DATA_CLEANUP_POLICY: 'data_cleanup_policy',
  
  /** 用户界面偏好 */
  UI_PREFERENCES: 'ui_preferences',
  
  /** 扩展启用状态 */
  EXTENSION_ENABLED: 'extension_enabled'
} as const;

/**
 * 默认设置值
 */
export const DEFAULT_SETTINGS = {
  [SETTING_KEYS.SEARCH_CONFIG]: {
    fuzzyThreshold: 0.3,
    maxResults: 50,
    includeScore: true
  },
  
  [SETTING_KEYS.BLACKLIST_DOMAINS]: [],
  
  [SETTING_KEYS.DATA_CLEANUP_POLICY]: {
    maxPages: 100000,
    maxAgeInDays: 365,
    autoCleanup: true
  },
  
  [SETTING_KEYS.UI_PREFERENCES]: {
    theme: 'auto',
    resultsPerPage: 20,
    showDomain: true,
    showTimestamp: true
  },
  
  [SETTING_KEYS.EXTENSION_ENABLED]: true
} as const;

/**
 * 数据库操作错误类型
 */
export class DBError extends Error {
  public code: string;
  public originalError?: Error;

  constructor(
    message: string,
    code: string,
    originalError?: Error
  ) {
    super(message);
    this.name = 'DBError';
    this.code = code;
    this.originalError = originalError;
  }
}

/**
 * 常见错误代码
 */
export const DB_ERROR_CODES = {
  DB_NOT_AVAILABLE: 'DB_NOT_AVAILABLE',
  STORE_NOT_FOUND: 'STORE_NOT_FOUND',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  DUPLICATE_KEY: 'DUPLICATE_KEY',
  ITEM_NOT_FOUND: 'ITEM_NOT_FOUND',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  NOT_FOUND: 'NOT_FOUND',
  MIGRATION_FAILED: 'MIGRATION_FAILED',
  STORAGE_QUOTA_WARNING: 'STORAGE_QUOTA_WARNING',
  LRU_CLEANUP_FAILED: 'LRU_CLEANUP_FAILED'
} as const;
