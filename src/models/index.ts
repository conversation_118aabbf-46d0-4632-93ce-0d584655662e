/**
 * 数据模型模块导出
 * 
 * 统一导出所有数据模型相关的类型和工具函数
 */

// 导出数据模型类型
export type {
  Page,
  Setting,
  BlacklistEntry,
  SearchResultItem,
  SearchOptions,
  PaginationOptions,
  PageFilter,
  PaginatedResult
} from './db.model';

// 导出设置相关类型
export type {
  SearchEngineSettings,
  SearchEngineConfig
} from './settings';

// 导出数据库配置和常量
export {
  DB_CONFIG,
  SETTING_KEYS,
  DEFAULT_SETTINGS,
  DBError,
  DB_ERROR_CODES
} from './db.model';

// 导出设置相关常量和工具函数
export {
  DEFAULT_SEARCH_ENGINE_CONFIG,
  validateSearchEngineConfig,
  normalizeSearchEngineWeights
} from './settings';

// 导出工具函数
export {
  generateUUID,
  extractDomain,
  cleanContent,
  cleanTitle,
  validatePage,
  createPage,
  updatePage,
  createSetting,
  updateSetting,
  shouldIndexUrl,
  formatTimestamp,
  estimatePageSize
} from './db.utils';
