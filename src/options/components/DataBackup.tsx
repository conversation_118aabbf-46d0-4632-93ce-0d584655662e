/**
 * Data Backup and Restore Component
 * 
 * Provides UI for exporting and importing extension data
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { dbService, blacklistService } from '../../services';
import { lunrSearchEngine } from '../../search/fulltext/LunrSearchEngine';
import { I18nManager } from '../../i18n/I18nManager';
import type { Page, BlacklistEntry } from '../../models';

/**
 * Data backup props
 */
interface DataBackupProps {
  className?: string;
}

/**
 * Export data structure
 */
interface ExportData {
  version: string;
  timestamp: number;
  pages: Page[];
  blacklist: BlacklistEntry[];
  metadata: {
    totalPages: number;
    totalBlacklist: number;
    exportedBy: string;
  };
}

/**
 * Import statistics
 */
interface ImportStats {
  pagesImported: number;
  blacklistImported: number;
  pagesSkipped: number;
  blacklistSkipped: number;
  errors: string[];
}

/**
 * Data statistics
 */
interface DataStats {
  totalPages: number;
  totalBlacklist: number;
}

/**
 * Data Backup Component
 */
export const DataBackup: React.FC<DataBackupProps> = ({ className = '' }) => {
  const [exporting, setExporting] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importStats, setImportStats] = useState<ImportStats | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  const [stats, setStats] = useState<DataStats | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // I18n setup
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, params?: Record<string, any>) => {
    return i18nManager.getTranslation(key, params);
  }, [i18nManager]);

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Load current language resources on mount
  useEffect(() => {
    i18nManager.loadLanguageResources(i18nManager.getCurrentLanguage()).catch(console.error);
  }, [i18nManager]);

  /**
   * Load data statistics
   */
  const loadStats = useCallback(async () => {
    try {
      const [totalPages, allBlacklistedDomains] = await Promise.all([
        dbService.getPageCount(),
        blacklistService.getAllDomains(),
      ]);
      setStats({ totalPages, totalBlacklist: allBlacklistedDomains.length });
    } catch (error) {
      console.error('Failed to load data stats:', error);
    }
  }, []);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  /**
   * Export all data to JSON file
   */
  const handleExport = useCallback(async () => {
    try {
      setExporting(true);
      setExportProgress(0);

      // Get all data
      setExportProgress(25);
      const pages = await dbService.getAllPages();
      
      setExportProgress(50);
      const blacklist = await blacklistService.getAllDomains();
      
      setExportProgress(75);

      // Create export data structure
      const exportData: ExportData = {
        version: '2.0.0',
        timestamp: Date.now(),
        pages,
        blacklist,
        metadata: {
          totalPages: pages.length,
          totalBlacklist: blacklist.length,
          exportedBy: 'Recall v2.0.0'
        }
      };

      // Create and download file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `recall-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setExportProgress(100);
      
      setTimeout(() => {
        setExportProgress(0);
        setExporting(false);
      }, 1000);

    } catch (error) {
      console.error('Export failed:', error);
      alert(t('options.dataBackup.export.error', { error: (error as Error).message }));
      setExporting(false);
      setExportProgress(0);
    }
  }, [t]);

  /**
   * Handle file selection for import
   */
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImport(file);
    }
  }, []);

  /**
   * Import data from JSON file
   */
  const handleImport = useCallback(async (file: File) => {
    try {
      setImporting(true);
      setImportStats(null);

      // Read file
      const text = await file.text();
      const data: ExportData = JSON.parse(text);

      // Validate data structure
      if (!data.version || !data.pages || !data.blacklist) {
        throw new Error(t('options.dataBackup.import.invalidFormat'));
      }

      const stats: ImportStats = {
        pagesImported: 0,
        blacklistImported: 0,
        pagesSkipped: 0,
        blacklistSkipped: 0,
        errors: []
      };

      // Import pages
      for (const page of data.pages) {
        try {
          // Check if page already exists by URL
          const existingPages = await dbService.getAllPages();
          const existing = existingPages.find(p => p.url === page.url);
          if (existing) {
            stats.pagesSkipped++;
          } else {
            // Use addPageForImport to preserve original timestamps
            await dbService.addPageForImport({
              url: page.url,
              title: page.title,
              content: page.content || '',
              visitTime: page.visitTime, // Preserve original visitTime
              accessCount: page.accessCount || 1, // Use accessCount or default to 1
              lastUpdated: page.lastUpdated || page.visitTime, // Use lastUpdated or fallback to visitTime
              contentStatus: page.contentStatus,
              extractionError: page.extractionError,
              lastExtractionAttempt: page.lastExtractionAttempt
            });
            stats.pagesImported++;
          }
        } catch (error) {
          stats.errors.push(`Failed to import page ${page.id}: ${error}`);
        }
      }

      // Import blacklist
      for (const entry of data.blacklist) {
        try {
          // Check if domain already exists
          const existing = await blacklistService.getDomainEntry(entry.domain);
          if (existing) {
            stats.blacklistSkipped++;
          } else {
            await blacklistService.addDomain(entry.domain, entry.reason, entry.isWildcard);
            stats.blacklistImported++;
          }
        } catch (error) {
          stats.errors.push(`Failed to import blacklist entry ${entry.domain}: ${error}`);
        }
      }

      setImportStats(stats);
      await loadStats(); // Refresh stats after import

      // After importing, trigger a full re-index of the search engine
      console.log('Rebuilding search index after import...');
      const allPages = await dbService.getAllPages();
      await lunrSearchEngine.createIndex(allPages);
      await lunrSearchEngine.saveIndexToStorage(); // Persist the new index
      console.log('Search index rebuilt successfully.');

    } catch (error) {
      console.error('Import failed:', error);
      alert(t('options.dataBackup.import.error', { error: (error as Error).message }));
    } finally {
      setImporting(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [loadStats, t]);

  /**
   * Clear all data
   */
  const handleClearAll = useCallback(async () => {
    const confirmed = confirm(
      t('options.dataBackup.dangerZone.clearAllConfirm')
    );
    
    if (!confirmed) return;

    const doubleConfirm = confirm(
      t('options.dataBackup.dangerZone.clearAllDoubleConfirm')
    );
    
    if (!doubleConfirm) return;

    try {
      await Promise.all([
        dbService.clearAllData(),
        blacklistService.clearAll()
      ]);

      alert(t('options.dataBackup.dangerZone.clearAllSuccess'));
      setImportStats(null);
      await loadStats(); // Refresh stats after clearing
    } catch (error) {
      console.error('Clear all failed:', error);
      alert(t('options.dataBackup.dangerZone.clearAllError'));
    }
  }, [loadStats, t]);

  return (
    <div className={`data-backup ${className}`}>
      {/* Header */}
      <div className="backup-header">
        <div className="backup-title-section">
          <h2>{t('options.dataBackup.title')}</h2>
          {stats && (
            <div className="backup-stats">
              <span>{t('options.dataBackup.stats.totalPages', { count: stats.totalPages })}</span>
              <span>{t('options.dataBackup.stats.totalBlacklist', { count: stats.totalBlacklist })}</span>
            </div>
          )}
        </div>
      </div>

      <div className="backup-content">
        {/* Export Section */}
        <div className="backup-section">
          <div className="section-header">
            <span className="icon">📤</span>
            <h3>{t('options.dataBackup.export.title')}</h3>
          </div>
          <p className="section-description">
            {t('options.dataBackup.export.description')}
          </p>
          <div className="section-content">
            {exporting && (
              <div className="progress-container">
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${exportProgress}%` }}
                  />
                </div>
                <span className="progress-text">{t('options.dataBackup.export.progress', { progress: exportProgress })}</span>
              </div>
            )}
            <button
              className="btn btn-primary"
              onClick={handleExport}
              disabled={exporting}
            >
              {exporting ? t('options.dataBackup.export.processing') : t('options.dataBackup.export.button')}
            </button>
          </div>
        </div>

        {/* Import Section */}
        <div className="backup-section">
          <div className="section-header">
            <span className="icon">📥</span>
            <h3>{t('options.dataBackup.import.title')}</h3>
          </div>
          <p className="section-description">
            {t('options.dataBackup.import.description')}
          </p>
          <div className="section-content">
            <input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
            <button
              className="btn btn-secondary"
              onClick={() => fileInputRef.current?.click()}
              disabled={importing}
            >
              {importing ? t('options.dataBackup.import.processing') : t('options.dataBackup.import.button')}
            </button>

            {importStats && (
              <div className="import-results">
                <h4>{t('options.dataBackup.importResults.title')}</h4>
                <div className="results-grid">
                  <div className="result-item success">
                    <span className="result-label">{t('options.dataBackup.importResults.pagesImported')}</span>
                    <span className="result-value">{t('options.dataBackup.importResults.entries', { count: importStats.pagesImported })}</span>
                  </div>
                  <div className="result-item success">
                    <span className="result-label">{t('options.dataBackup.importResults.blacklistImported')}</span>
                    <span className="result-value">{t('options.dataBackup.importResults.entries', { count: importStats.blacklistImported })}</span>
                  </div>
                  <div className="result-item warning">
                    <span className="result-label">{t('options.dataBackup.importResults.pagesSkipped')}</span>
                    <span className="result-value">{t('options.dataBackup.importResults.entries', { count: importStats.pagesSkipped })}</span>
                  </div>
                  <div className="result-item warning">
                    <span className="result-label">{t('options.dataBackup.importResults.blacklistSkipped')}</span>
                    <span className="result-value">{t('options.dataBackup.importResults.entries', { count: importStats.blacklistSkipped })}</span>
                  </div>
                </div>
                
                {importStats.errors.length > 0 && (
                  <div className="import-errors">
                    <h5>{t('options.dataBackup.importResults.importErrors')}</h5>
                    <ul>
                      {importStats.errors.slice(0, 5).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                      {importStats.errors.length > 5 && (
                        <li>{t('options.dataBackup.importResults.moreErrors', { count: importStats.errors.length - 5 })}</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Danger Zone */}
        <div className="backup-section danger-zone">
          <div className="section-header">
            <span className="icon">⚠️</span>
            <h3>{t('options.dataBackup.dangerZone.title')}</h3>
          </div>
          <div className="section-content">
            <div className="warning-box">
              <strong>⚠️ Warning: </strong>
              {t('options.dataBackup.dangerZone.description')}
            </div>
            <button
              className="btn btn-danger"
              onClick={handleClearAll}
            >
              {t('options.dataBackup.dangerZone.clearAllButton')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
