/**
 * Blacklist Management Component
 * 
 * Provides UI for managing domain blacklist
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { blacklistService } from '../../services';
import type { BlacklistEntry } from '../../models';
import { I18nManager } from '../../i18n/I18nManager';
import { logger } from '../../utils/logger';
import './BlacklistManagement.css';

/**
 * 黑名单管理页面属性
 */
interface BlacklistManagementProps {
  className?: string;
}

/**
 * 新增黑名单条目的表单数据
 */
interface BlacklistFormData {
  domain: string;
  reason: string;
  isWildcard: boolean;
}

/**
 * 黑名单统计数据
 */
interface BlacklistStats {
  totalDomains: number;
  wildcardDomains: number;
}

/**
 * 帮助提示组件
 */
const HelpTooltip: React.FC<{ t: (key: string) => string }> = ({ t }) => (
  <div className="help-tooltip">
    💡
    <div className="help-tooltip-content">
      <h4>{t('options.blacklistManagement.help.title')}</h4>
      <ul>
        <li><strong>{t('options.blacklistManagement.help.exactMatch')}</strong></li>
        <li><strong>{t('options.blacklistManagement.help.wildcardMatch')}</strong></li>
        <li><strong>{t('options.blacklistManagement.help.autoEffect')}</strong></li>
        <li><strong>{t('options.blacklistManagement.help.existingData')}</strong></li>
      </ul>
    </div>
  </div>
);

/**
 * Add New Domain Modal
 */
const AddDomainModal: React.FC<{
  onClose: () => void;
  onSuccess: () => void;
  t: (key: string, interpolation?: Record<string, any>) => string;
}> = ({ onClose, onSuccess, t }) => {
  const [formData, setFormData] = useState<BlacklistFormData>({
    domain: '',
    reason: '',
    isWildcard: false,
  });
  const [formErrors, setFormErrors] = useState<Partial<BlacklistFormData>>({});
  const [submitting, setSubmitting] = useState(false);

  const validateForm = (data: BlacklistFormData): Partial<BlacklistFormData> => {
    const errors: Partial<BlacklistFormData> = {};
    if (!data.domain.trim()) {
      errors.domain = t('options.blacklistManagement.addDomain.errors.domainRequired');
    } else if (!/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(data.domain.trim()) && !data.isWildcard) {
      errors.domain = t('options.blacklistManagement.addDomain.errors.domainInvalid');
    } else if (data.isWildcard && !data.domain.startsWith('*.')) {
      errors.domain = t('options.blacklistManagement.addDomain.errors.wildcardInvalid');
    }
    return errors;
  };

  const handleInputChange = (field: keyof BlacklistFormData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateForm(formData);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    try {
      setSubmitting(true);
      await blacklistService.addDomain(
        formData.domain.trim(),
        formData.reason.trim() || undefined,
        formData.isWildcard,
      );
      onSuccess();
    } catch (error) {
      console.error('Failed to add domain:', error);
      alert(t('options.blacklistManagement.addDomain.addFailed'));
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <form onSubmit={handleSubmit} className="add-form">
          <h3>{t('options.blacklistManagement.addDomain.modalTitle')}</h3>
          <div className="form-group">
            <label htmlFor="domain">{t('options.blacklistManagement.addDomain.domainLabel')}</label>
            <input
              id="domain"
              type="text"
              value={formData.domain}
              onChange={(e) => handleInputChange('domain', e.target.value)}
              placeholder={formData.isWildcard ? t('options.blacklistManagement.addDomain.domainPlaceholderWildcard') : t('options.blacklistManagement.addDomain.domainPlaceholder')}
              className={formErrors.domain ? 'error' : ''}
              autoComplete="off"
            />
            {formErrors.domain && <span className="error-message">{formErrors.domain}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="reason">{t('options.blacklistManagement.addDomain.reasonLabel')}</label>
            <input
              id="reason"
              type="text"
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              placeholder={t('options.blacklistManagement.addDomain.reasonPlaceholder')}
            />
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={formData.isWildcard}
                onChange={(e) => handleInputChange('isWildcard', e.target.checked)}
              />
              <span className="checkbox-text">{t('options.blacklistManagement.addDomain.wildcardLabel')}</span>
            </label>
          </div>

          <div className="form-actions">
            <button type="submit" className="btn btn-primary" disabled={submitting}>
              {submitting ? t('options.blacklistManagement.addDomain.submitButtonLoading') : t('options.blacklistManagement.addDomain.submitButton')}
            </button>
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              {t('options.blacklistManagement.addDomain.cancelButton')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

/**
 * Blacklist Item Component
 */
const BlacklistItem: React.FC<{
  entry: BlacklistEntry;
  onDelete: (domain: string) => void;
  searchKeywords: string[];
  t: (key: string, interpolation?: Record<string, any>) => string;
}> = ({ entry, onDelete, searchKeywords, t }) => {

  const highlight = (text: string) => {
    if (!searchKeywords.length || !text) return text;
    const regex = new RegExp(`(${searchKeywords.join('|')})`, 'gi');
    const parts = text.split(regex);
    return (
      <>
        {parts.map((part, i) =>
          regex.test(part) ? <mark key={i}>{part}</mark> : <span key={i}>{part}</span>
        )}
      </>
    );
  };

  return (
    <div className="blacklist-item">
      <div className="blacklist-item-favicon">
        <img
          src={`https://www.google.com/s2/favicons?domain=${entry.domain.replace('*.', '')}&sz=32`}
          alt=""
          onError={(e) => { e.currentTarget.style.display = 'none'; }}
        />
      </div>
      <div className="blacklist-item-main">
        <div className="blacklist-item-domain">
          {highlight(entry.domain)}
        </div>
        <div className="blacklist-item-reason">
          {highlight(entry.reason || t('options.blacklistManagement.domainList.noReason'))}
        </div>
      </div>
      <div className="blacklist-item-aside">
        <div className="blacklist-meta">
          <span className="meta-item" title={entry.isWildcard ? t('options.blacklistManagement.domainList.wildcardMatchTooltip') : t('options.blacklistManagement.domainList.exactMatchTooltip')}>
            {entry.isWildcard ? `🌐 ${t('options.blacklistManagement.domainList.wildcardMatch')}` : `🔗 ${t('options.blacklistManagement.domainList.exactMatch')}`}
          </span>
          <span className="meta-item" title={t('options.blacklistManagement.domainList.addedTimeTooltip', { time: new Date(entry.createdAt).toLocaleString() })}>
            📅 {new Date(entry.createdAt).toLocaleDateString()}
          </span>
        </div>
        <div className="blacklist-actions">
          <button
            className="delete-btn"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(entry.domain);
            }}
            title={t('options.blacklistManagement.domainList.removeTooltip')}
            aria-label={t('options.blacklistManagement.domainList.removeAriaLabel', { domain: entry.domain })}
          >
            🗑️
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Empty State
 */
const EmptyState: React.FC<{ 
  onAdd: () => void; 
  isSearch: boolean; 
  query: string;
  t: (key: string, interpolation?: Record<string, any>) => string;
}> = ({ onAdd, isSearch, query, t }) => (
  <div className="empty-state">
    <div className="empty-icon">
      {isSearch ? '🔍' : '🛡️'}
    </div>
    <h3>{isSearch ? t('options.blacklistManagement.emptyState.noResults') : t('options.blacklistManagement.emptyState.empty')}</h3>
    <p>{isSearch ? t('options.blacklistManagement.emptyState.noResultsDescription', { query }) : t('options.blacklistManagement.emptyState.emptyDescription')}</p>
    {!isSearch && (
      <button className="btn btn-primary" onClick={onAdd}>
        {t('options.blacklistManagement.emptyState.addFirstDomain')}
      </button>
    )}
  </div>
);

/**
 * Blacklist Management Component
 */
export const BlacklistManagement: React.FC<BlacklistManagementProps> = ({ className = '' }) => {
  const [entries, setEntries] = useState<BlacklistEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<BlacklistStats | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // i18n setup
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, interpolation?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolation);
  }, [i18nManager]);

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Load current language resources on mount
  useEffect(() => {
    i18nManager.loadLanguageResources(i18nManager.getCurrentLanguage()).catch(console.error);
  }, [i18nManager]);

  /**
   * Load blacklist entries and stats
   */
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const [entriesData, statsData] = await Promise.all([
        blacklistService.getAllDomains(),
        blacklistService.getStats(),
      ]);

      setEntries(entriesData);
      setStats(statsData);
    } catch (error) {
      logger.error(error as Error, 'Failed to load blacklist data');
    } finally {
      setLoading(false);
    }
  }, []);

  const filteredEntries = useMemo(() => {
    if (!searchQuery.trim()) {
      return entries;
    }
    return entries.filter(entry =>
      entry.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      entry.reason?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [entries, searchQuery]);

  const searchKeywords = useMemo(() => {
    return searchQuery.trim() ? searchQuery.trim().toLowerCase().split(/\s+/).filter(Boolean) : [];
  }, [searchQuery]);

  /**
   * Handle successful addition
   */
  const handleAddSuccess = () => {
    setShowAddModal(false);
    loadData();
  };

  /**
   * Handle domain deletion
   */
  const handleDelete = async (domain: string) => {
    if (!confirm(t('options.blacklistManagement.confirmations.removeDomain', { domain }))) {
      return;
    }

    try {
      await blacklistService.removeDomain(domain);
      await loadData();
    } catch (error) {
      console.error('Failed to delete domain:', error);
      alert(t('options.blacklistManagement.confirmations.deleteFailed'));
    }
  };

  /**
   * Handle bulk operations
   */
  const handleClearAll = async () => {
    if (!confirm(t('options.blacklistManagement.confirmations.clearAll', { count: entries.length.toString() }))) {
      return;
    }

    try {
      await blacklistService.clearAll();
      await loadData();
    } catch (error) {
      console.error('Failed to clear blacklist:', error);
      alert(t('options.blacklistManagement.confirmations.clearFailed'));
    }
  };

  // Load initial data
  useEffect(() => {
    loadData();
  }, [loadData]);

  return (
    <div className={`blacklist-management ${className}`}>
      {/* Header */}
      <div className="blacklist-header">
        <div className="blacklist-title-section">
          <h2>{t('options.blacklistManagement.title')}</h2>
          {stats && (
            <div className="blacklist-stats">
              <span>{t('options.blacklistManagement.stats.totalDomains', { count: stats.totalDomains })}</span>
              <span>{t('options.blacklistManagement.stats.wildcardDomains', { count: stats.wildcardDomains })}</span>
            </div>
          )}
        </div>

        <div className="blacklist-controls">
          <div className="search-section">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t('options.blacklistManagement.search.placeholder')}
              className="blacklist-search"
            />
          </div>

          <div className="actions-section">
            <button
              className="btn btn-danger"
              onClick={handleClearAll}
              disabled={entries.length === 0 || loading}
              title={t('options.blacklistManagement.bulkActions.clearAllTooltip')}
            >
              {t('options.blacklistManagement.bulkActions.clearAll')}
            </button>
            <button
              className="btn btn-primary"
              onClick={() => setShowAddModal(true)}
              title={t('options.blacklistManagement.bulkActions.addDomainTooltip')}
            >
              {t('options.blacklistManagement.bulkActions.addDomain')}
            </button>
            <HelpTooltip t={t} />
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="blacklist-list-container">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner-large" />
            <span>{t('options.blacklistManagement.loading')}</span>
          </div>
        ) : filteredEntries.length === 0 ? (
          <EmptyState
            onAdd={() => setShowAddModal(true)}
            isSearch={!!searchQuery.trim()}
            query={searchQuery}
            t={t}
          />
        ) : (
          <div className="blacklist-virtual-list">
            {filteredEntries.map((entry) => (
              <BlacklistItem
                key={entry.domain}
                entry={entry}
                onDelete={handleDelete}
                searchKeywords={searchKeywords}
                t={t}
              />
            ))}
          </div>
        )}
      </div>

      {/* Add Modal */}
      {showAddModal && <AddDomainModal onClose={() => setShowAddModal(false)} onSuccess={handleAddSuccess} t={t} />}
    </div>
  );
};

