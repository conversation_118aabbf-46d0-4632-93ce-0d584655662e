# CLAUDE.local.md - Recall Project Best Practices Guide

This document provides comprehensive best practices and guidelines for working with the Recall Chrome Extension project. It serves as a reference for developers, AI assistants, and contributors.

## <� Project Overview

Recall is a privacy-first Chrome Extension that transforms browser history into an intelligent personal knowledge base using local AI processing. Key features include semantic search, automatic knowledge organization, and BYOK (Bring Your Own Key) support for advanced AI features.

**Core Principles:**
- = **Privacy First**: All data processing happens locally
- � **Performance**: Sub-300ms semantic search on 10k+ pages
- >� **Modular**: Clean architecture with separation of concerns
- = **Security**: AES-256 encryption for sensitive data
- =� **User Experience**: Non-intrusive, intelligent assistance

## =� Project Structure Best Practices

### Directory Organization
```
src/
   ai/          # AI engines and LLM integrations
   background/  # Service worker components
   content/     # Content scripts for page interaction
   storage/     # Data persistence and caching
   search/      # Search engine implementations
   security/    # Encryption and key management
   services/    # Core business logic services
   components/  # Shared UI components
   popup/       # Extension popup UI
   options/     # Settings page UI
   knowledge/   # Knowledge management algorithms
```

### Module Patterns
- **Single Responsibility**: Each module has one clear purpose
- **Export Pattern**: Use index.ts files for clean exports
- **Singleton Services**: Database and core services use singleton pattern
- **Factory Pattern**: For creating complex objects (AI engines)
- **Strategy Pattern**: For swappable algorithms (search methods)

## =� Development Workflow

### Git Workflow
```bash
# Branch naming convention
{project}-{task}-{short-uuid}
# Example: recall-semantic-search-a3f2

# Commit message format
<type>: <description>
# Types: feat, fix, test, refactor, docs, perf, security
```

### Essential Commands
```bash
# Development
npm run dev          # Start development server with HMR
npm run dev:mock     # Start mock server for testing

# Testing
npm run test:all     # Run complete test suite
npm run test:unit    # Unit tests with coverage
npm run test:e2e     # End-to-end tests
npm run test:perf    # Performance benchmarks

# Building
npm run build        # Production build (Vite)
npm run lint         # Code quality checks
```

### Pre-commit Checks
The project uses extensive pre-commit hooks. Always ensure:
1. Code is formatted (Prettier)
2. TypeScript compiles without errors
3. Tests pass
4. Build size is under 5MB
5. No security vulnerabilities

## <� Architecture Best Practices

### TypeScript Patterns

#### Type-Safe Constants
```typescript
//  Good: Type-safe enum pattern
export const TaskType = {
  INDEX_PAGE: 'index_page',
  UPDATE_PAGE: 'update_page',
} as const;
export type TaskType = typeof TaskType[keyof typeof TaskType];

// L Avoid: String literals scattered in code
const type = 'index_page'; // Magic string
```

#### Error Handling
```typescript
//  Good: Custom error classes with context
export class DBError extends Error {
  constructor(
    message: string,
    public code: DBErrorCode,
    public details?: any
  ) {
    super(message);
    this.name = 'DBError';
  }
}

//  Good: Comprehensive error handling
try {
  await operation();
} catch (error) {
  if (error instanceof DBError && error.code === DBErrorCode.QUOTA_EXCEEDED) {
    await this.performLRUCleanup();
  }
  throw error;
}
```

#### Async/Await Best Practices
```typescript
//  Good: Promise wrapper for IndexedDB
private promiseRequest<T>(request: IDBRequest<T>): Promise<T> {
  return new Promise((resolve, reject) => {
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

//  Good: Timeout handling
async withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<T> {
  const timeout = new Promise<never>((_, reject) => 
    setTimeout(() => reject(new Error('Timeout')), timeoutMs)
  );
  return Promise.race([promise, timeout]);
}
```

### Service Layer Patterns

#### Singleton Services
```typescript
export class IndexedDBService {
  private static instance: IndexedDBService;
  
  static getInstance(): IndexedDBService {
    if (!IndexedDBService.instance) {
      IndexedDBService.instance = new IndexedDBService();
    }
    return IndexedDBService.instance;
  }
  
  private constructor() {
    // Private constructor prevents direct instantiation
  }
}
```

#### Batch Processing
```typescript
//  Good: Efficient batch operations
async processBatch<T>(
  items: T[],
  processor: (item: T) => Promise<void>,
  batchSize = 50
): Promise<void> {
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    await Promise.all(batch.map(processor));
  }
}
```

### Performance Optimizations

#### Resource Monitoring
```typescript
//  Good: Adaptive processing based on resources
if (this.resourceMonitor.isHighLoad()) {
  await this.delay(1000); // Throttle when system is busy
}
```

#### Memory Management
```typescript
//  Good: LRU cache with size limits
class LRUCacheManager {
  private readonly maxSize = 500 * 1024 * 1024; // 500MB
  
  async ensureSpace(requiredSize: number): Promise<void> {
    const currentSize = await this.getCurrentSize();
    if (currentSize + requiredSize > this.maxSize) {
      await this.performLRUCleanup(requiredSize);
    }
  }
}
```

## >� Testing Best Practices

### Test Organization
```typescript
describe('ServiceName', () => {
  let service: ServiceName;
  
  beforeEach(() => {
    jest.clearAllMocks();
    service = new ServiceName();
  });
  
  describe('methodName', () => {
    it('should handle success case', async () => {
      // Arrange
      const input = createTestInput();
      
      // Act
      const result = await service.methodName(input);
      
      // Assert
      expect(result).toMatchExpectedOutput();
    });
    
    it('should handle error case', async () => {
      // Test error scenarios
    });
  });
});
```

### Mock Strategies
```typescript
//  Good: Comprehensive Chrome API mocking
global.chrome = {
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({}),
    onMessage: {
      addListener: jest.fn(),
    },
  },
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(undefined),
    },
  },
};
```

## = Security Best Practices

### API Key Management
```typescript
//  Good: Encrypted storage with salt
export class KeyStorage {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly ITERATIONS = 100000; // PBKDF2
  
  async storeKey(key: string): Promise<void> {
    const salt = crypto.getRandomValues(new Uint8Array(16));
    const encrypted = await this.encrypt(key, salt);
    await chrome.storage.local.set({ encrypted, salt });
  }
}
```

### Input Validation
```typescript
//  Good: Validate all external inputs
validateUrl(url: string): boolean {
  const urlPattern = /^https?:\/\/.+/;
  return urlPattern.test(url) && !this.isBlacklisted(url);
}
```

## =� Performance Guidelines

### Target Metrics
- **Semantic Search**: < 250ms end-to-end
- **Text Embedding**: < 45ms per 1000 characters
- **Page Indexing**: < 30s background processing
- **Memory Usage**: < 120MB peak
- **Storage**: 500MB limit with LRU cleanup

### Optimization Techniques
1. **Batch Operations**: Process items in chunks
2. **Debouncing**: Rate-limit expensive operations
3. **Lazy Loading**: Load resources on demand
4. **Virtual Scrolling**: For large result sets
5. **Web Workers**: Offload heavy computations

## <� UI/UX Best Practices

### Extension Popup
- **Size**: 400x600px (Chrome standard)
- **Loading**: < 100ms perceived load time
- **Accessibility**: WCAG 2.1 AA compliance
- **Keyboard**: Full keyboard navigation support

### Content Scripts
- **Non-intrusive**: Minimal visual footprint
- **Performance**: No impact on page load
- **Compatibility**: Work with 95%+ websites
- **Error Handling**: Graceful degradation

## =� Documentation Standards

### Code Documentation
```typescript
/**
 * Performs semantic search using HNSW algorithm
 * 
 * @param query - Natural language search query
 * @param options - Search configuration options
 * @returns Promise resolving to ranked search results
 * 
 * @example
 * const results = await search('react hooks optimization', {
 *   limit: 10,
 *   threshold: 0.7
 * });
 */
```

### File Headers
```typescript
/**
 * @module search/semantic
 * @description Implements semantic search using vector embeddings
 * 
 * Key features:
 * - HNSW algorithm for fast nearest neighbor search
 * - Hybrid scoring with temporal relevance
 * - Fallback to keyword search
 */
```

## =� Chrome Extension Specifics

### Manifest V3 Compliance
- Use service workers instead of background pages
- Declare all permissions explicitly
- Use declarativeNetRequest for network filtering
- Implement proper CSP headers

### Message Passing
```typescript
//  Good: Type-safe message passing
interface MessageRequest {
  type: 'SEARCH' | 'INDEX' | 'DELETE';
  payload: any;
}

chrome.runtime.onMessage.addListener(
  (request: MessageRequest, sender, sendResponse) => {
    switch (request.type) {
      case 'SEARCH':
        handleSearch(request.payload).then(sendResponse);
        return true; // Keep channel open for async response
    }
  }
);
```

### Storage Management
```typescript
//  Good: Efficient storage with compression
const compressed = await compress(data);
await chrome.storage.local.set({ 
  [`page_${id}`]: compressed 
});
```

## = Continuous Improvement

### Performance Monitoring
- Regular performance benchmarks
- Track key metrics in production
- A/B test optimization changes
- Monitor user feedback

### Code Quality
- Maintain >80% test coverage
- Regular dependency updates
- Security audits
- Architecture reviews

### Developer Experience
- Keep build times fast (< 30s)
- Provide helpful error messages
- Document complex algorithms
- Maintain up-to-date examples

## =� Task Management

### When Working on Features
1. Always use TodoWrite to track tasks
2. Break large features into smaller tasks
3. Mark tasks as in-progress when starting
4. Complete tasks immediately when done
5. Update progress in docs/task-tracker.json

### Planning Format
```markdown
## Task: [Feature Name]
- [ ] Research existing implementation
- [ ] Design solution architecture  
- [ ] Implement core functionality
- [ ] Add comprehensive tests
- [ ] Update documentation
- [ ] Performance optimization
```

## <� Common Pitfalls to Avoid

1. **Large Classes**: Break down files > 500 lines
2. **Magic Numbers**: Extract to named constants
3. **Deep Nesting**: Use early returns
4. **Ignored Errors**: Always handle exceptions
5. **Missing Tests**: Test edge cases
6. **Poor Names**: Use descriptive variable names
7. **Tight Coupling**: Maintain loose coupling
8. **Memory Leaks**: Clean up event listeners
9. **Blocking Operations**: Use async patterns
10. **Security Shortcuts**: Never compromise security

## = Quick Reference

### Key Files
- `src/services/db.service.ts` - Database operations
- `src/ai/transformers/TransformersJSEngine.ts` - AI engine
- `src/search/SemanticSearch.ts` - Search implementation
- `src/background/IndexScheduler.ts` - Background tasks
- `src/security/CryptoService.ts` - Encryption

### Important Configurations
- `vite.config.ts` - Build configuration
- `manifest.json` - Extension manifest
- `jest.config.cjs` - Test configuration
- `.pre-commit-config.yaml` - Git hooks

### External Documentation
- [Chrome Extension Docs](https://developer.chrome.com/docs/extensions/mv3/)
- [HNSW Algorithm Paper](https://arxiv.org/abs/1603.09320)
- [Transformers.js Docs](https://huggingface.co/docs/transformers.js)

### Workflow Reminders
- prd in @docs/prd/ ,when you try to fix testcase or something, see it frist before doing something.

---

*Last Updated: 2025-06-25*
*This is a living document. Update it as the project evolves.*