{"permissions": {"allow": ["Bash(npm run test:unit:*)", "Bash(npm run test:all:*)", "Bash(npm test:*)", "Bash(grep:*)", "Bash(npm run test:e2e:*)", "Bash(npm run build:*)", "Bash(npm run test:*)", "Bash(time npm run build)", "Bash(timeout 10 npm run dev:mock)", "Bash(timeout 5 npm run dev:mock)", "mcp__sequentialthinking__sequentialthinking", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(for i in {1..4})", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(done)", "Bash(JEST_CONFIG_PATH=/Users/<USER>/Dat/worktree/agent3-config-ux/jest.config.cjs npx jest /Users/<USER>/Dat/worktree/agent3-config-ux/src/utils/debounce.test.ts --no-coverage)", "<PERSON><PERSON>(command:*)", "Bash(timeout 30s npm test -- src/options/components/debounce.test.ts --testNamePattern=\"should debounce input changes with exactly 500ms delay\" --no-coverage)", "Bash(timeout 60s npm test -- src/options/components/debounce.test.ts --testNamePattern=\"500ms Debounce Timing Accuracy\" --no-coverage)", "Bash(timeout 60s npm test -- src/options/components/debounce.test.ts --testNamePattern=\"Multiple Rapid Inputs Debouncing\" --no-coverage)", "Bash(timeout 60s npm test -- src/options/components/debounce.test.ts --testNamePattern=\"Proper Batching of Configuration Changes\" --no-coverage)", "mcp__zen__consensus", "mcp__zen__thinkdeep", "mcp__zen__chat", "Bash(timeout 30s npm test src/tests/security/api-key-security.test.ts --verbose)", "mcp__zen__debug", "Bash(npm test src/pro/reports/PDFGenerator.test.ts -- --testNamePattern=\"应该成功创建基础PDF文档\" --no-coverage)", "Bash(grep -n \"\\.toLocaleDateString\\|\\.toLocaleString\" /Users/<USER>/Dat/Recall/src/pro/reports/PDFGenerator.ts)", "Bash(rm:*)", "Bash(/dev/null)", "<PERSON><PERSON>(true)", "mcp__zen__analyze", "Bash(./scripts/run-advanced-search-test.sh:*)", "<PERSON><PERSON>(pkill:*)", "Bash(timeout 10s npm run dev)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["playwright"]}